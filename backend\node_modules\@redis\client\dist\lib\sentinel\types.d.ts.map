{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../lib/sentinel/types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,WAAW,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AACzI,OAAO,QAAQ,MAAM,aAAa,CAAC;AACnC,OAAO,aAAa,EAAE,EAAE,mBAAmB,EAAE,MAAM,GAAG,CAAC;AACvD,OAAO,EAAE,qBAAqB,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,qBAAqB,EAAE,6BAA6B,EAAE,MAAM,iBAAiB,CAAC;AAEvF,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,oBAAoB,CACnC,CAAC,SAAS,YAAY,GAAG,YAAY,EACrC,CAAC,SAAS,cAAc,GAAG,cAAc,EACzC,CAAC,SAAS,YAAY,GAAG,YAAY,EACrC,IAAI,SAAS,YAAY,GAAG,YAAY,EACxC,YAAY,SAAS,WAAW,GAAG,WAAW,CAC9C,SAAQ,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IACtD;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,iBAAiB,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;IACpC;;OAEG;IACH,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAE/B;;OAEG;IACH,iBAAiB,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE,qBAAqB,CAAC,CAAC;IAE9H;;OAEG;IACH,qBAAqB,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE,qBAAqB,CAAC,CAAC;IAClI;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;;OAGG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;;;OAKG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;;;OAKG;IACH,4BAA4B,CAAC,EAAE,OAAO,CAAC;IACvC;;;OAGG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,eAAe,CAAC,EAAE,6BAA6B,GAAG,qBAAqB,CAAC;CACzE;AAED,MAAM,WAAW,iBAAiB,CAChC,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,CAEhC,SAAQ,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACtC,cAAc,CAAC,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;CAC/C;AAED,MAAM,MAAM,0BAA0B,GAAG,IAAI,CAC3C,kBAAkB,EAClB,MAAM,iBAAiB,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAsB,CACpH,CAAC;AAEF,KAAK,YAAY,CACf,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,gBAAgB,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CACzF,CAAC;AAEF,KAAK,WAAW,CACd,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACjE;CACF,CAAC;AAEF,KAAK,aAAa,CAChB,CAAC,SAAS,cAAc,EACxB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG;SACb,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;KACjE;CACF,CAAC;AAEF,KAAK,WAAW,CACd,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,IAC9B;KACD,CAAC,IAAI,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;CAC3D,CAAC;AAEF,MAAM,MAAM,uBAAuB,CACjC,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,IACnC,CACF,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAChD,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,GAChC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAClC,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACpC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CACnC,CAAC;AAEF,MAAM,MAAM,iBAAiB,CAC3B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,IAEnC,CACF,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAC1C,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,GAChC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GAClC,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,GACpC,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CACnC,CAAC;AAEF,MAAM,WAAW,sBAAsB,CACrC,YAAY,SAAS,WAAW,GAAG,WAAW,CAC9C,SAAQ,cAAc,CAAC,YAAY,CAAC;CAAG;AAEzC,MAAM,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACnE,MAAM,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/E,MAAM,MAAM,sBAAsB,GAAG;IAAE,KAAK,EAAE,aAAa,CAAA;CAAE,CAAC;AAC9D,MAAM,MAAM,4BAA4B,GAAG;IAAE,KAAK,EAAE,mBAAmB,CAAA;CAAE,CAAC;AAE1E,MAAM,MAAM,QAAQ,GAAG;IACrB,EAAE,EAAE,GAAG,CAAC;IACR,IAAI,EAAE,GAAG,CAAC;IACV,KAAK,EAAE,GAAG,CAAC;CACZ,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG,eAAe,GAAG,eAAe,CAAC;AAEnE,MAAM,MAAM,eAAe,GAAG;IAC5B,IAAI,EAAE,iBAAiB,GAAG,eAAe,GAAG,aAAa,GAAG,gBAAgB,CAAC;IAC7E,IAAI,EAAE,SAAS,CAAC;CACjB,CAAA;AAED,MAAM,MAAM,eAAe,GAAG;IAC5B,IAAI,EAAE,qBAAqB,CAAC;IAC5B,IAAI,EAAE,MAAM,CAAC;CACd,CAAA;AAED,MAAM,MAAM,gBAAgB,GAAG;IAC7B,IAAI,EAAE,QAAQ,GAAG,SAAS,GAAG,UAAU,GAAG,aAAa,CAAC;IACxD,IAAI,EAAE,SAAS,CAAC;IAChB,KAAK,EAAE,KAAK,CAAC;CACd,CAAA"}