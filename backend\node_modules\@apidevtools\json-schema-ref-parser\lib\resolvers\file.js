"use strict";
const fs = require("fs");
const { ono } = require("@jsdevtools/ono");
const url = require("../util/url");
const { ResolverError } = require("../util/errors");

module.exports = {
  /**
   * The order that this resolver will run, in relation to other resolvers.
   *
   * @type {number}
   */
  order: 100,

  /**
   * Determines whether this resolver can read a given file reference.
   * Resolvers that return true will be tried, in order, until one successfully resolves the file.
   * Resolvers that return false will not be given a chance to resolve the file.
   *
   * @param {object} file           - An object containing information about the referenced file
   * @param {string} file.url       - The full URL of the referenced file
   * @param {string} file.extension - The lowercased file extension (e.g. ".txt", ".html", etc.)
   * @returns {boolean}
   */
  canRead (file) {
    return url.isFileSystemPath(file.url);
  },

  /**
   * Reads the given file and returns its raw contents as a Buffer.
   *
   * @param {object} file           - An object containing information about the referenced file
   * @param {string} file.url       - The full URL of the referenced file
   * @param {string} file.extension - The lowercased file extension (e.g. ".txt", ".html", etc.)
   * @returns {Promise<Buffer>}
   */
  read (file) {
    return new Promise(((resolve, reject) => {
      let path;
      try {
        path = url.toFileSystemPath(file.url);
      }
      catch (err) {
        reject(new ResolverError(ono.uri(err, `Malformed URI: ${file.url}`), file.url));
      }

      // console.log('Opening file: %s', path);

      try {
        fs.readFile(path, (err, data) => {
          if (err) {
            reject(new ResolverError(ono(err, `Error opening file "${path}"`), path));
          }
          else {
            resolve(data);
          }
        });
      }
      catch (err) {
        reject(new ResolverError(ono(err, `Error opening file "${path}"`), path));
      }
    }));
  }
};
