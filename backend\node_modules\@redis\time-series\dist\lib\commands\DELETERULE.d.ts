import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, SimpleStringReply } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Deletes a compaction rule between source and destination time series
     * @param parser - The command parser
     * @param sourceKey - The source time series key
     * @param destinationKey - The destination time series key
     */
    readonly parseCommand: (this: void, parser: CommandParser, sourceKey: RedisArgument, destinationKey: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=DELETERULE.d.ts.map