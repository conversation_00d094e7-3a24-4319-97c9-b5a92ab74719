"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Removes an existing alias from a RediSearch index.
     * @param parser - The command parser
     * @param alias - The alias to remove
     */
    parseCommand(parser, alias) {
        parser.push('FT.ALIASDEL', alias);
    },
    transformReply: undefined
};
//# sourceMappingURL=ALIASDEL.js.map