const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Service name is required'],
    trim: true,
    maxlength: [100, 'Service name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Service description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: String,
    required: [true, 'Service category is required'],
    enum: ['plumbing', 'electrical', 'hvac', 'appliance', 'carpentry', 'painting', 'roofing', 'cleaning', 'other']
  },
  basePrice: {
    type: Number,
    required: [true, 'Base price is required'],
    min: [0, 'Price cannot be negative']
  },
  estimatedDuration: {
    type: Number, // in hours
    required: [true, 'Estimated duration is required'],
    min: [0.5, 'Duration must be at least 30 minutes']
  },
  image: {
    type: String,
    default: 'default-service.png'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  requirements: [{
    type: String,
    maxlength: [200, 'Requirement cannot exceed 200 characters']
  }],
  tags: [{
    type: String,
    lowercase: true,
    trim: true
  }],
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  warranty: {
    duration: { type: Number, default: 30 }, // days
    description: { type: String, default: '30-day service warranty' }
  },
  // Pricing tiers based on complexity
  pricingTiers: [{
    name: { type: String, required: true }, // e.g., 'Basic', 'Standard', 'Premium'
    description: String,
    price: { type: Number, required: true },
    features: [String]
  }]
}, {
  timestamps: true
});

// Indexes for better query performance
serviceSchema.index({ category: 1 });
serviceSchema.index({ isActive: 1 });
serviceSchema.index({ basePrice: 1 });
serviceSchema.index({ tags: 1 });
serviceSchema.index({ name: 'text', description: 'text' });

module.exports = mongoose.model('Service', serviceSchema);
