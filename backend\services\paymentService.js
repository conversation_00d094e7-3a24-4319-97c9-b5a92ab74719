const stripe = process.env.STRIPE_SECRET_KEY ? require('stripe')(process.env.STRIPE_SECRET_KEY) : null;
const Booking = require('../models/Booking');
const User = require('../models/User');

class PaymentService {
  // Create payment intent for a booking
  async createPaymentIntent(bookingId, userId) {
    if (!stripe) {
      throw new Error('Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.');
    }

    try {
      const booking = await Booking.findById(bookingId).populate('service');
      if (!booking) {
        throw new Error('Booking not found');
      }

      if (booking.customer.toString() !== userId) {
        throw new Error('Unauthorized access to booking');
      }

      if (booking.paymentStatus === 'paid') {
        throw new Error('Booking already paid');
      }

      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(booking.totalAmount * 100), // Convert to cents
        currency: 'usd',
        metadata: {
          bookingId: bookingId,
          userId: userId,
          serviceId: booking.service._id.toString()
        },
        description: `Payment for ${booking.service.name} - Booking #${booking._id}`
      });

      // Update booking with payment intent ID
      booking.paymentIntentId = paymentIntent.id;
      booking.paymentStatus = 'pending';
      await booking.save();

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: booking.totalAmount
      };
    } catch (error) {
      console.error('Payment intent creation error:', error);
      throw error;
    }
  }

  // Confirm payment and update booking status
  async confirmPayment(paymentIntentId) {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      
      if (paymentIntent.status === 'succeeded') {
        const booking = await Booking.findOne({ paymentIntentId });
        if (booking) {
          booking.paymentStatus = 'paid';
          booking.status = 'confirmed';
          booking.paidAt = new Date();
          await booking.save();
          
          return { success: true, booking };
        }
      }
      
      return { success: false, status: paymentIntent.status };
    } catch (error) {
      console.error('Payment confirmation error:', error);
      throw error;
    }
  }

  // Create refund for a booking
  async createRefund(bookingId, userId, amount = null) {
    try {
      const booking = await Booking.findById(bookingId);
      if (!booking) {
        throw new Error('Booking not found');
      }

      if (booking.customer.toString() !== userId && !await this.isAdmin(userId)) {
        throw new Error('Unauthorized access to booking');
      }

      if (booking.paymentStatus !== 'paid') {
        throw new Error('Booking not paid, cannot refund');
      }

      const refundAmount = amount || Math.round(booking.totalAmount * 100);
      
      const refund = await stripe.refunds.create({
        payment_intent: booking.paymentIntentId,
        amount: refundAmount,
        metadata: {
          bookingId: bookingId,
          userId: userId
        }
      });

      // Update booking status
      booking.paymentStatus = 'refunded';
      booking.status = 'cancelled';
      booking.refundedAt = new Date();
      booking.refundAmount = refundAmount / 100;
      await booking.save();

      return { success: true, refund, booking };
    } catch (error) {
      console.error('Refund creation error:', error);
      throw error;
    }
  }

  // Get payment history for a user
  async getPaymentHistory(userId) {
    try {
      const bookings = await Booking.find({ 
        customer: userId,
        paymentStatus: { $in: ['paid', 'refunded'] }
      })
      .populate('service', 'name category')
      .sort({ paidAt: -1 });

      return bookings.map(booking => ({
        id: booking._id,
        service: booking.service.name,
        category: booking.service.category,
        amount: booking.totalAmount,
        paymentStatus: booking.paymentStatus,
        paidAt: booking.paidAt,
        refundedAt: booking.refundedAt,
        refundAmount: booking.refundAmount
      }));
    } catch (error) {
      console.error('Payment history error:', error);
      throw error;
    }
  }

  // Helper method to check if user is admin
  async isAdmin(userId) {
    const user = await User.findById(userId);
    return user && user.role === 'admin';
  }

  // Generate invoice data
  async generateInvoice(bookingId) {
    try {
      const booking = await Booking.findById(bookingId)
        .populate('customer', 'firstName lastName email')
        .populate('service', 'name description price category')
        .populate('technician', 'firstName lastName');

      if (!booking) {
        throw new Error('Booking not found');
      }

      return {
        invoiceNumber: `INV-${booking._id}`,
        bookingId: booking._id,
        customer: {
          name: `${booking.customer.firstName} ${booking.customer.lastName}`,
          email: booking.customer.email
        },
        service: {
          name: booking.service.name,
          description: booking.service.description,
          category: booking.service.category,
          price: booking.service.price
        },
        technician: booking.technician ? {
          name: `${booking.technician.firstName} ${booking.technician.lastName}`
        } : null,
        scheduledDate: booking.scheduledDate,
        totalAmount: booking.totalAmount,
        paymentStatus: booking.paymentStatus,
        paidAt: booking.paidAt,
        createdAt: booking.createdAt
      };
    } catch (error) {
      console.error('Invoice generation error:', error);
      throw error;
    }
  }
}

module.exports = new PaymentService();
