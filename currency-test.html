<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FCFA Currency Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .price-example {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        .price-value {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin: 10px 0;
        }
        .old-price {
            text-decoration: line-through;
            color: #999;
            margin-right: 15px;
        }
        .new-price {
            color: #10b981;
            font-weight: bold;
        }
        .service-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: white;
        }
        .service-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .service-price {
            font-size: 20px;
            color: #2563eb;
            font-weight: bold;
        }
        .conversion-note {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇨🇫 Currency Changed to FCFA</h1>
        
        <div class="conversion-note">
            <strong>Conversion Rate:</strong> 1 USD ≈ 600 FCFA<br>
            All prices have been converted from USD to Central African CFA franc (FCFA)
        </div>

        <h2>📋 Service Pricing Examples</h2>

        <div class="service-card">
            <div class="service-name">🔧 Pipe Leak Repair</div>
            <div class="service-price">
                <span class="old-price">$120</span>
                <span class="new-price">72 000 FCFA</span>
            </div>
        </div>

        <div class="service-card">
            <div class="service-name">⚡ Electrical Outlet Installation</div>
            <div class="service-price">
                <span class="old-price">$150</span>
                <span class="new-price">90 000 FCFA</span>
            </div>
        </div>

        <div class="service-card">
            <div class="service-name">❄️ AC Unit Maintenance</div>
            <div class="service-price">
                <span class="old-price">$200</span>
                <span class="new-price">120 000 FCFA</span>
            </div>
        </div>

        <div class="service-card">
            <div class="service-name">🍽️ Dishwasher Repair</div>
            <div class="service-price">
                <span class="old-price">$180</span>
                <span class="new-price">108 000 FCFA</span>
            </div>
        </div>

        <div class="service-card">
            <div class="service-name">💨 Ceiling Fan Installation</div>
            <div class="service-price">
                <span class="old-price">$175</span>
                <span class="new-price">105 000 FCFA</span>
            </div>
        </div>

        <h2>💰 Currency Formatting Features</h2>

        <div class="price-example">
            <strong>Standard Format:</strong>
            <div class="price-value" id="standard-format"></div>
        </div>

        <div class="price-example">
            <strong>With Decimals:</strong>
            <div class="price-value" id="decimal-format"></div>
        </div>

        <div class="price-example">
            <strong>Large Amount:</strong>
            <div class="price-value" id="large-format"></div>
        </div>

        <div class="price-example">
            <strong>Price Range:</strong>
            <div class="price-value" id="range-format"></div>
        </div>
    </div>

    <script>
        // Currency formatting function (same as in the app)
        function formatFCFA(amount, showDecimals = false) {
            if (amount === null || amount === undefined || isNaN(amount)) {
                return '0 FCFA';
            }

            const formatter = new Intl.NumberFormat('fr-CF', {
                minimumFractionDigits: showDecimals ? 2 : 0,
                maximumFractionDigits: showDecimals ? 2 : 0,
                useGrouping: true
            });

            return formatter.format(amount) + ' FCFA';
        }

        // Display examples
        document.getElementById('standard-format').textContent = formatFCFA(72000);
        document.getElementById('decimal-format').textContent = formatFCFA(72000, true);
        document.getElementById('large-format').textContent = formatFCFA(1250000);
        document.getElementById('range-format').textContent = formatFCFA(50000) + ' - ' + formatFCFA(150000);
    </script>
</body>
</html>
