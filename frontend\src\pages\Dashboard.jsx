import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { bookingService } from '../services/bookingService';
import { serviceService } from '../services/serviceService';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalBookings: 0,
    pendingBookings: 0,
    completedBookings: 0,
    totalServices: 0
  });
  const [recentBookings, setRecentBookings] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Temporarily use mock data for development
      setRecentBookings([]);
      setStats({
        totalBookings: 5,
        pendingBookings: 2,
        completedBookings: 3,
        totalServices: 10
      });
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#f59e0b';
      case 'confirmed': return '#3b82f6';
      case 'assigned': return '#8b5cf6';
      case 'in-progress': return '#06b6d4';
      case 'completed': return '#10b981';
      case 'cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
            <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <p className="text-gray-600 font-medium">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container">
        {/* Modern Welcome Header */}
        <div className="mb-12 animate-fadeInUp">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Welcome back, {user?.firstName}! 👋
              </h1>
              <p className="text-xl text-gray-600">
                {user?.role === 'admin' && 'Manage your repair and maintenance business'}
                {user?.role === 'technician' && 'View your assigned jobs and schedule'}
                {user?.role === 'customer' && 'Track your service requests and bookings'}
              </p>
            </div>
            <div className="hidden md:block">
              <div className="w-20 h-20 bg-gradient-primary rounded-2xl flex items-center justify-center">
                <span className="text-3xl text-white font-bold">
                  {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                </span>
              </div>
            </div>
          </div>
        </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-8" style={{ gap: '1.5rem' }}>
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Bookings</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalBookings}</p>
            </div>
            <div style={{ fontSize: '2rem' }}>📋</div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingBookings}</p>
            </div>
            <div style={{ fontSize: '2rem' }}>⏳</div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">{stats.completedBookings}</p>
            </div>
            <div style={{ fontSize: '2rem' }}>✅</div>
          </div>
        </div>

        {user?.role === 'admin' && (
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Services</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalServices}</p>
              </div>
              <div style={{ fontSize: '2rem' }}>🔧</div>
            </div>
          </div>
        )}

        {user?.role === 'technician' && (
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rating</p>
                <p className="text-2xl font-bold text-gray-900">
                  {user?.rating?.average?.toFixed(1) || 'N/A'}
                </p>
              </div>
              <div style={{ fontSize: '2rem' }}>⭐</div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mb-8" style={{ gap: '1.5rem' }}>
        {user?.role === 'customer' && (
          <>
            <Link to="/services" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
              <div className="text-center">
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔍</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Browse Services</h3>
                <p className="text-gray-600">Find and book repair services</p>
              </div>
            </Link>
            <Link to="/bookings" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
              <div className="text-center">
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📅</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">My Bookings</h3>
                <p className="text-gray-600">View your service requests</p>
              </div>
            </Link>
          </>
        )}

        {user?.role === 'technician' && (
          <>
            <Link to="/bookings" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
              <div className="text-center">
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔧</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">My Jobs</h3>
                <p className="text-gray-600">View assigned jobs</p>
              </div>
            </Link>
            <div className="card">
              <div className="text-center">
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Performance</h3>
                <p className="text-gray-600">View your ratings and stats</p>
              </div>
            </div>
          </>
        )}

        {user?.role === 'admin' && (
          <>
            <Link to="/admin/bookings" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
              <div className="text-center">
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📋</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manage Bookings</h3>
                <p className="text-gray-600">Assign technicians and track progress</p>
              </div>
            </Link>
            <Link to="/services" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
              <div className="text-center">
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🛠️</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manage Services</h3>
                <p className="text-gray-600">Add and edit services</p>
              </div>
            </Link>
          </>
        )}

        <Link to="/profile" className="card" style={{ textDecoration: 'none', color: 'inherit' }}>
          <div className="text-center">
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>👤</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Profile</h3>
            <p className="text-gray-600">Update your information</p>
          </div>
        </Link>
      </div>

      {/* Recent Bookings */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Recent Bookings</h2>
          <Link to="/bookings" className="btn btn-secondary">
            View All
          </Link>
        </div>

        {recentBookings.length === 0 ? (
          <div className="text-center py-8">
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>📋</div>
            <p className="text-gray-600">No bookings yet</p>
            {user?.role === 'customer' && (
              <Link to="/services" className="btn btn-primary mt-4">
                Book Your First Service
              </Link>
            )}
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #e5e7eb' }}>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600' }}>Booking #</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600' }}>Service</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600' }}>Date</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600' }}>Status</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', fontWeight: '600' }}>Action</th>
                </tr>
              </thead>
              <tbody>
                {recentBookings.map((booking) => (
                  <tr key={booking._id} style={{ borderBottom: '1px solid #f3f4f6' }}>
                    <td style={{ padding: '0.75rem' }}>
                      <span className="font-medium">{booking.bookingNumber}</span>
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      {booking.service?.name || 'Service'}
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      {formatDate(booking.scheduledDate)}
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      <span style={{
                        padding: '0.25rem 0.75rem',
                        borderRadius: '9999px',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                        backgroundColor: getStatusColor(booking.status) + '20',
                        color: getStatusColor(booking.status)
                      }}>
                        {booking.status}
                      </span>
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      <Link
                        to={`/bookings/${booking._id}`}
                        className="text-sm"
                        style={{ color: '#2563eb', textDecoration: 'none' }}
                      >
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
