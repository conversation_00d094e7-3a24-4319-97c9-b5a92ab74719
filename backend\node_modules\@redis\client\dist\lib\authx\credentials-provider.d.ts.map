{"version": 3, "file": "credentials-provider.d.ts", "sourceRoot": "", "sources": ["../../../lib/authx/credentials-provider.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC,QAAQ,CAAC,IAAI,EAAE,4BAA4B,CAAC;IAC5C,WAAW,EAAE,MAAM,OAAO,CAAC,SAAS,CAAC,CAAA;CACtC;AAED;;;GAGG;AACH,MAAM,WAAW,4BAA4B;IAC3C,QAAQ,CAAC,IAAI,EAAE,gCAAgC,CAAC;IAEhD;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,SAAS,EAAE,CAAC,QAAQ,EAAE,4BAA4B,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAA;IAElG;;;;;;OAMG;IACH,uBAAuB,EAAE,CAAC,KAAK,EAAE,qBAAqB,KAAK,IAAI,CAAC;CAEjE;AAED;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG;IAAE,QAAQ,CAAC,EAAE,MAAM,CAAC;IAAC,QAAQ,CAAC,EAAE,MAAM,CAAA;CAAE,CAAA;AAEhE;;GAEG;AACH,MAAM,MAAM,4BAA4B,CAAC,CAAC,IAAI;IAC5C,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,IAAI,CAAC;IACjC,OAAO,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC;CAC7B,CAAA;AAGD;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG,wBAAwB,GAAG,4BAA4B,CAAA;AAEzF;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG,gBAAgB,GAAG,iCAAiC,CAAA;AAExF;;;;GAIG;AACH,qBAAa,gBAAiB,SAAQ,KAAK;gBAC7B,OAAO,EAAE,MAAM;CAK5B;AAED;;GAEG;AACH,qBAAa,iCAAkC,SAAQ,KAAK;gBAC9C,OAAO,EAAE,MAAM;CAI5B"}