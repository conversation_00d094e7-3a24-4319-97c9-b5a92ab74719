{"version": 3, "file": "XPENDING_RANGE.js", "sourceRoot": "", "sources": ["../../../lib/commands/XPENDING_RANGE.ts"], "names": [], "mappings": ";;AA6BA,kBAAe;IACb,SAAS,EAAE,IAAI;IACf,YAAY,EAAE,IAAI;IAClB;;;;;;;;;;;;OAYG;IACH,YAAY,CACV,MAAqB,EACrB,GAAkB,EAClB,KAAoB,EACpB,KAAoB,EACpB,GAAkB,EAClB,KAAa,EACb,OAA8B;QAE9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEnB,IAAI,OAAO,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE1C,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IACD;;;;;OAKG;IACH,cAAc,CAAC,KAAyC;QACtD,OAAO,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACzB,MAAM,SAAS,GAAG,OAAiD,CAAC;YACpE,OAAO;gBACL,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC;gBAChB,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;gBACtB,6BAA6B,EAAE,SAAS,CAAC,CAAC,CAAC;gBAC3C,iBAAiB,EAAE,SAAS,CAAC,CAAC,CAAC;aAChC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACyB,CAAC"}