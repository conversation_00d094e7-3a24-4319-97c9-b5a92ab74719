const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Repair & Maintenance Services API',
      version: '1.0.0',
      description: 'A comprehensive API for managing repair and maintenance services',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production' 
          ? 'https://your-api-domain.com/api'
          : 'http://localhost:5001/api',
        description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          required: ['firstName', 'lastName', 'email', 'password', 'phone'],
          properties: {
            _id: {
              type: 'string',
              description: 'User ID'
            },
            firstName: {
              type: 'string',
              description: 'User first name'
            },
            lastName: {
              type: 'string',
              description: 'User last name'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            phone: {
              type: 'string',
              description: 'User phone number'
            },
            role: {
              type: 'string',
              enum: ['customer', 'technician', 'admin'],
              description: 'User role'
            },
            isActive: {
              type: 'boolean',
              description: 'User account status'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation date'
            }
          }
        },
        Service: {
          type: 'object',
          required: ['name', 'description', 'category', 'price'],
          properties: {
            _id: {
              type: 'string',
              description: 'Service ID'
            },
            name: {
              type: 'string',
              description: 'Service name'
            },
            description: {
              type: 'string',
              description: 'Service description'
            },
            category: {
              type: 'string',
              enum: ['plumbing', 'electrical', 'hvac', 'general'],
              description: 'Service category'
            },
            price: {
              type: 'number',
              description: 'Service base price'
            },
            duration: {
              type: 'number',
              description: 'Estimated duration in hours'
            },
            isActive: {
              type: 'boolean',
              description: 'Service availability status'
            }
          }
        },
        Booking: {
          type: 'object',
          required: ['service', 'customer', 'scheduledDate', 'problemDescription'],
          properties: {
            _id: {
              type: 'string',
              description: 'Booking ID'
            },
            service: {
              $ref: '#/components/schemas/Service'
            },
            customer: {
              $ref: '#/components/schemas/User'
            },
            technician: {
              $ref: '#/components/schemas/User'
            },
            status: {
              type: 'string',
              enum: ['pending', 'confirmed', 'assigned', 'in-progress', 'completed', 'cancelled'],
              description: 'Booking status'
            },
            scheduledDate: {
              type: 'string',
              format: 'date-time',
              description: 'Scheduled service date'
            },
            problemDescription: {
              type: 'string',
              description: 'Description of the problem'
            },
            pricing: {
              type: 'object',
              properties: {
                basePrice: {
                  type: 'number'
                },
                totalAmount: {
                  type: 'number'
                }
              }
            },
            payment: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  enum: ['pending', 'paid', 'failed', 'refunded']
                },
                method: {
                  type: 'string',
                  enum: ['cash', 'card', 'bank_transfer', 'digital_wallet', 'stripe']
                }
              }
            }
          }
        },
        Review: {
          type: 'object',
          required: ['booking', 'customer', 'rating'],
          properties: {
            _id: {
              type: 'string',
              description: 'Review ID'
            },
            booking: {
              type: 'string',
              description: 'Booking ID'
            },
            customer: {
              $ref: '#/components/schemas/User'
            },
            rating: {
              type: 'number',
              minimum: 1,
              maximum: 5,
              description: 'Rating from 1 to 5'
            },
            comment: {
              type: 'string',
              description: 'Review comment'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              description: 'Error message'
            },
            errors: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: 'Validation errors'
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              description: 'Success message'
            },
            data: {
              type: 'object',
              description: 'Response data'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./routes/*.js', './models/*.js'], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(options);

module.exports = specs;
