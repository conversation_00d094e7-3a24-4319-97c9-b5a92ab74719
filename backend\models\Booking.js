const mongoose = require('mongoose');

const bookingSchema = new mongoose.Schema({
  bookingNumber: {
    type: String,
    unique: true,
    required: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: [true, 'Service is required']
  },
  technician: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'assigned', 'in-progress', 'completed', 'cancelled', 'rescheduled'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  scheduledDate: {
    type: Date,
    required: [true, 'Scheduled date is required']
  },
  scheduledTime: {
    start: {
      type: String,
      required: false, // Made optional for easier booking creation
      validate: {
        validator: function(v) {
          return !v || /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
        },
        message: 'Start time must be in HH:MM format'
      }
    },
    end: {
      type: String,
      required: false, // Made optional for easier booking creation
      validate: {
        validator: function(v) {
          return !v || /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
        },
        message: 'End time must be in HH:MM format'
      }
    }
  },
  actualStartTime: Date,
  actualEndTime: Date,
  serviceAddress: {
    street: { type: String, required: false }, // Made optional for easier booking creation
    city: { type: String, required: false },   // Made optional for easier booking creation
    state: { type: String, required: false },  // Made optional for easier booking creation
    zipCode: { type: String, required: false }, // Made optional for easier booking creation
    country: { type: String, default: 'USA' },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  problemDescription: {
    type: String,
    required: [true, 'Problem description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  images: [{
    url: String,
    description: String,
    uploadedAt: { type: Date, default: Date.now }
  }],
  pricing: {
    basePrice: { type: Number, required: true },
    additionalCharges: [{
      description: String,
      amount: Number
    }],
    discount: { type: Number, default: 0 },
    tax: { type: Number, default: 0 },
    totalAmount: { type: Number, required: true }
  },
  payment: {
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded'],
      default: 'pending'
    },
    method: {
      type: String,
      enum: ['cash', 'card', 'bank_transfer', 'digital_wallet']
    },
    transactionId: String,
    paidAt: Date
  },
  notes: {
    customer: String,
    technician: String,
    admin: String
  },
  estimatedDuration: Number, // in hours
  actualDuration: Number,    // in hours
  materials: [{
    name: String,
    quantity: Number,
    unitPrice: Number,
    totalPrice: Number
  }],
  beforeImages: [String],
  afterImages: [String],
  customerSignature: String,
  technicianSignature: String,
  completionReport: {
    workPerformed: String,
    issuesFound: String,
    recommendations: String,
    warrantyInfo: String
  },
  feedback: {
    rating: { type: Number, min: 1, max: 5 },
    comment: String,
    submittedAt: Date
  },
  cancellationReason: String,
  rescheduledFrom: Date,
  rescheduledTo: Date,
  isEmergency: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Generate booking number before saving
bookingSchema.pre('save', async function(next) {
  if (!this.bookingNumber) {
    const count = await mongoose.model('Booking').countDocuments();
    this.bookingNumber = `BK${Date.now().toString().slice(-6)}${(count + 1).toString().padStart(3, '0')}`;
  }
  next();
});

// Indexes for better query performance (bookingNumber index is already created by unique: true)
bookingSchema.index({ customer: 1 });
bookingSchema.index({ technician: 1 });
bookingSchema.index({ service: 1 });
bookingSchema.index({ status: 1 });
bookingSchema.index({ scheduledDate: 1 });
bookingSchema.index({ priority: 1 });

module.exports = mongoose.model('Booking', bookingSchema);
