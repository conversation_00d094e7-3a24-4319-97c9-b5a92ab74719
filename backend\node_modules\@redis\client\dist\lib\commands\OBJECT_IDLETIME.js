"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    IS_READ_ONLY: true,
    /**
     * Constructs the OBJECT IDLETIME command
     *
     * @param parser - The command parser
     * @param key - The key to get the idle time for
     * @see https://redis.io/commands/object-idletime/
     */
    parseCommand(parser, key) {
        parser.push('OBJECT', 'IDLETIME');
        parser.pushKey(key);
    },
    transformReply: undefined
};
//# sourceMappingURL=OBJECT_IDLETIME.js.map