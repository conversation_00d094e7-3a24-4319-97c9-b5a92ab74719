## Change Log

### v6.0.9 (2021/03/07 10:34 +00:00)

- [#256](https://github.com/Surnet/swagger-jsdoc/pull/256) fix(anchors): applied a fix for anchors living in seperate files with… (#256) (@goldsziggy)

### v7.0.0-rc.4 (2021/03/01 16:00 +00:00)

- [15bce04](https://github.com/Surnet/swagger-jsdoc/commit/15bce0415b4dfff7ca21d8931b5498e71a9013ff) correction: v6.0.8 (@kalinchernev)

### v6.0.8 (2021/03/01 15:20 +00:00)

- [fc3d62c](https://github.com/Surnet/swagger-jsdoc/commit/fc3d62c61a266b879fd8ab1531f77634645f0247) v6.0.7 (@kalinchernev)
- [#253](https://github.com/Surnet/swagger-jsdoc/pull/253) fix(specification): apply fix for multiple anchors (#253) (@goldsziggy)

### v7.0.0-rc.3 (2021/02/26 14:33 +00:00)

- [8873370](https://github.com/Surnet/swagger-jsdoc/commit/887337050ec3b2d246d2fa9a9bd3570b3746d379) Update docs (@kalinchernev)
- [#250](https://github.com/Surnet/swagger-jsdoc/pull/250) Update FIRST-STEPS.md (#250) (@azizkale)
- [da152ea](https://github.com/Surnet/swagger-jsdoc/commit/da152eaf81656b77f49628005225ef7e0f647c9e) Update docs (@kalinchernev)

### v6.0.6 (2021/02/16 09:01 +00:00)

- [32540d1](https://github.com/Surnet/swagger-jsdoc/commit/32540d1f36fc286c72d260f4690465744ba08ca0) docs version bump (@kalinchernev)
- [12a4e6d](https://github.com/Surnet/swagger-jsdoc/commit/12a4e6d9cdcbd2c9d7497413425ac9dedffcdbcc) Update landing page readme (@kalinchernev)
- [c65d659](https://github.com/Surnet/swagger-jsdoc/commit/c65d659b6df554b1e0cc2552d4340cdfdc27f4c3) Update issue templates (@kalinchernev)

### v6.0.5 (2021/02/15 15:52 +00:00)

- [05bc5a9](https://github.com/Surnet/swagger-jsdoc/commit/05bc5a91b8dc008243c74109ffd557ef5ed9d8a8) bump (@kalinchernev)
- [a56522f](https://github.com/Surnet/swagger-jsdoc/commit/a56522f11560727fc7327f7c0e9c083d8517dd2e) changelog (@kalinchernev)
- [#247](https://github.com/Surnet/swagger-jsdoc/pull/247) feat: support cjs and update docs (#247) (@kalinchernev)

### v6.0.3 (2021/02/15 08:40 +00:00)

- [#245](https://github.com/Surnet/swagger-jsdoc/pull/245) fix yaml formatting (#245) (@kalinchernev)

### v6.0.2 (2021/01/29 11:02 +00:00)

- [87e51e9](https://github.com/Surnet/swagger-jsdoc/commit/87e51e9688aa75994d38735369cf210a65813159) chore: prepare v6.0.2 (@kalinchernev)
- [#241](https://github.com/Surnet/swagger-jsdoc/pull/241) Issue with error message when using deprecated "apis" key (#241) (@CleyFaye)

### v6.0.1 (2021/01/07 06:23 +00:00)

- [92d51ba](https://github.com/Surnet/swagger-jsdoc/commit/92d51bacae77f4bc3d421872dea33ce366b7a108) update version (@kalinchernev)
- [#238](https://github.com/Surnet/swagger-jsdoc/pull/238) fix: yaml export format (#238) (@kalinchernev)

### v6.0.0 (2020/12/23 09:38 +00:00)

- [7aaa3e0](https://github.com/Surnet/swagger-jsdoc/commit/7aaa3e00b75c6deffcce09126beee391716549d2) Update TYPESCRIPT.md (@kalinchernev)
- [d2965db](https://github.com/Surnet/swagger-jsdoc/commit/d2965dbf919956010bcc156e29fa086986e324e3) Release 6.0 (@kalinchernev)
- [0d0be79](https://github.com/Surnet/swagger-jsdoc/commit/0d0be797263a6f95dd0efcc5386b37b8757f8ecb) Update documentation (@kalinchernev)
- [6db97e2](https://github.com/Surnet/swagger-jsdoc/commit/6db97e25832e0f94f87db9c5d5340122efe4f385) update changelog (@kalinchernev)
- [a72a91f](https://github.com/Surnet/swagger-jsdoc/commit/a72a91f6262172b0bb49f089039cc176f1564d6c) prepare rc5 (@kalinchernev)

### v6.0.0-rc.5 (2020/12/16 08:56 +00:00)

- [#235](https://github.com/Surnet/swagger-jsdoc/pull/235) feat: support x-webhooks (#235) (@kalinchernev)
- [348687e](https://github.com/Surnet/swagger-jsdoc/commit/348687ea0faffe47bf842fe4ecf22abf92bd00a2) Simplify docs (@kalinchernev)
- [d2b194c](https://github.com/Surnet/swagger-jsdoc/commit/d2b194c82ea2956d8c424c6c3b1b1269c18a94a9) Update changelog (@kalinchernev)

### v6.0.0-rc.4 (2020/12/12 17:15 +00:00)

- [#234](https://github.com/Surnet/swagger-jsdoc/pull/234) Remove 'api' attr from swagger definition (#234) (@mits87)

### v6.0.0-rc.3 (2020/11/28 15:37 +00:00)

- [7ec0825](https://github.com/Surnet/swagger-jsdoc/commit/7ec08259e1ca36343be0a7ed5e3a2475b28b325a) chore: prepare v6.0.0-rc.3 (@kalinchernev)
- [d99fbd5](https://github.com/Surnet/swagger-jsdoc/commit/d99fbd56599cbabbb3eaeee1497a5060e4e4bbc4) chore: prepare v6.0.0-rc.3 (@kalinchernev)
- [9686d62](https://github.com/Surnet/swagger-jsdoc/commit/9686d62adbca6544241ac47027a23f8aa05a379b) docs: update examples (@kalinchernev)
- [c4cea4c](https://github.com/Surnet/swagger-jsdoc/commit/c4cea4caaf94624d63e67be68f1e72a47d776cdc) docs: update changelog upcoming (@kalinchernev)
- [74395f2](https://github.com/Surnet/swagger-jsdoc/commit/74395f243d5e8977d8b20a253a90320318bc03f1) feat: support custom encoding in api files (@kalinchernev)
- [270c0af](https://github.com/Surnet/swagger-jsdoc/commit/270c0af310d9c0090b4f9f48ad051565d5f4bf7e) documentation fixes (@kalinchernev)
- [d217725](https://github.com/Surnet/swagger-jsdoc/commit/d2177254e9424fd4c93fea92c45b45c41d927e15) remove github-changes because of vulnerabilities (@kalinchernev)
- [5908281](https://github.com/Surnet/swagger-jsdoc/commit/590828123201910b7ed62105a53719431635fb8a) add changelog (@kalinchernev)

### v6.0.0-rc.2 (2020/11/28 09:34 +00:00)

- [3379925](https://github.com/Surnet/swagger-jsdoc/commit/337992556529bd2030a6e512dfb7951945fe7026) sync published version (@kalinchernev)
- [#229](https://github.com/Surnet/swagger-jsdoc/pull/229) Add empty array fallback to regex match (#229) (@evans)

### v6.0.0-rc.1 (2020/11/27 06:38 +00:00)

- [2a08842](https://github.com/Surnet/swagger-jsdoc/commit/2a08842ab3a970c117358d2a667e5a598ee54d70) bump (@kalinchernev)
- [#227](https://github.com/Surnet/swagger-jsdoc/pull/227) feat: handle yaml references between separate documents (#227) (@kalinchernev)

### v5.0.1 (2020/11/08 08:22 +00:00)

- [#224](https://github.com/Surnet/swagger-jsdoc/pull/224) Update README.md (#224) (@zeevo)

### v5.0.0 (2020/10/28 15:36 +00:00)

- [#220](https://github.com/Surnet/swagger-jsdoc/pull/220) chore: refactor helpers (#220) (@kalinchernev)

### v4.3.1 (2020/10/22 06:10 +00:00)

- [b95f784](https://github.com/Surnet/swagger-jsdoc/commit/b95f784783b5915259c62e78cb401e58bb3b18b4) bump version (@kalinchernev)
- [bf44557](https://github.com/Surnet/swagger-jsdoc/commit/bf4455707464783ba30ca48c38bb1dd25d6525b5) add back coverage ignore-s (@kalinchernev)
- [c3cdcc5](https://github.com/Surnet/swagger-jsdoc/commit/c3cdcc508a876c2888b92a1d34a0aa31aceb0a02) refactor cli a bit (@kalinchernev)
- [2b7d6a8](https://github.com/Surnet/swagger-jsdoc/commit/2b7d6a8aee06793a571f452b7cedc70404f04520) add specs for public functions (@kalinchernev)
- [105f461](https://github.com/Surnet/swagger-jsdoc/commit/105f4610c7a42bac55a7120b4c845885e6c0bcd3) coverage for yaml malformatting (@kalinchernev)
- [1f2d9d6](https://github.com/Surnet/swagger-jsdoc/commit/1f2d9d6873e7b6c024d32a8346025300534f6208) increase coverage for initial error handling (@kalinchernev)
- [9058113](https://github.com/Surnet/swagger-jsdoc/commit/90581132d817c82777cc70341d20d8a7c26d60e2) remove deprecated coverage ignore lines (@kalinchernev)
- [0d66d5c](https://github.com/Surnet/swagger-jsdoc/commit/0d66d5ceaa141e21e97a940665e71c59f97ae186) remove @requires (@kalinchernev)
- [068581a](https://github.com/Surnet/swagger-jsdoc/commit/068581a1eb46649e3720d04a4d4b3321bd3f474c) remove unnecessary @function annotations (@kalinchernev)
- [a19d7e0](https://github.com/Surnet/swagger-jsdoc/commit/a19d7e04f10a6eb5c227ba4012caeaec0eb413f7) remove @module definition (@kalinchernev)
- [fe97fee](https://github.com/Surnet/swagger-jsdoc/commit/fe97fee707a9afd12b8cc32184bac03843402a12) remove vscode settings (@kalinchernev)

### v4.3.0 (2020/10/13 13:47 +00:00)

- [#52](https://github.com/Surnet/swagger-jsdoc/pull/52) feat: add coffeescript support (#52) (@Aslan11)

### v4.2.3 (2020/10/08 14:58 +00:00)

- [#214](https://github.com/Surnet/swagger-jsdoc/pull/214) chore: use jest over mocha (#214) (@kalinchernev)

### v4.2.2 (2020/10/08 14:49 +00:00)

- [#217](https://github.com/Surnet/swagger-jsdoc/pull/217) make a test with patch version (#217) (@kalinchernev)

### v4.2.1 (2020/10/08 14:44 +00:00)

- [e3a670b](https://github.com/Surnet/swagger-jsdoc/commit/e3a670baf214b8e23f2f992df72654ef92f9e0ae) update github workflow (@kalinchernev)
- [3017902](https://github.com/Surnet/swagger-jsdoc/commit/30179029e9da52833fdfc231eb286aea9c89fdcb) update docs (@kalinchernev)
- [8e9da9b](https://github.com/Surnet/swagger-jsdoc/commit/8e9da9b1f780f1dbe10b3921ba4d2e9e8791ddb4) remove circle ci (@kalinchernev)
- [2644db6](https://github.com/Surnet/swagger-jsdoc/commit/2644db6a36c53ad7253592a5d884d4b0f828e2d2) update readme (@kalinchernev)
- [#213](https://github.com/Surnet/swagger-jsdoc/pull/213) Add github actions (#213) (@kalinchernev)
- [98d7cf4](https://github.com/Surnet/swagger-jsdoc/commit/98d7cf4ed408fa95614d7421398005d73aab021f) Update version (@kalinchernev)

### v4.2.0 (2020/09/25 10:47 +00:00)

- [#200](https://github.com/Surnet/swagger-jsdoc/pull/200) add openapi jsdoc annotation with test (#200) (@Uzlopak)
- [#208](https://github.com/Surnet/swagger-jsdoc/pull/208) Better describing errors (#208) (@allisonverdam)

### v4.1.0 (2020/09/25 10:24 +00:00)

- [f508180](https://github.com/Surnet/swagger-jsdoc/commit/f508180239ad90b92712f01edf0e5f0fdea2e276) Update dependencies (@kalinchernev)

### v4.0.0 (2020/03/22 13:18 +00:00)

- [5ab0e06](https://github.com/Surnet/swagger-jsdoc/commit/5ab0e06bba19866fa98880068c816fa76fd6c664) Upgrade to Node Dubnium (@kalinchernev)

### v3.7.0 (2020/03/22 13:09 +00:00)

- [a9c0a24](https://github.com/Surnet/swagger-jsdoc/commit/a9c0a24ebc757612cc7caf20972a482362d40d3f) Downgrade deps to node carbon (@kalinchernev)
- [d105727](https://github.com/Surnet/swagger-jsdoc/commit/d105727f1ca90cbe17c449848ebbf627e63b4f5d) Update mocka opts (@kalinchernev)
- [#191](https://github.com/Surnet/swagger-jsdoc/pull/191) Bump acorn from 7.1.0 to 7.1.1 (#191) (@dependabot[bot])

### v3.6.0 (2020/03/22 12:32 +00:00)

- [598b7e7](https://github.com/Surnet/swagger-jsdoc/commit/598b7e7e405ff224e1b38be1cdacac2f7918989d) Upgrade deps + bump to 3.6.0 (@kalinchernev)

### v3.5.0 (2019/12/04 17:38 +00:00)

- [9b5b392](https://github.com/Surnet/swagger-jsdoc/commit/9b5b3920121310c29d9857f49f554db8e9170bee) Release 3.5.0 (@kalinchernev)
- [#183](https://github.com/Surnet/swagger-jsdoc/pull/183) feat: update commander to latest version (#183) (@jamesburns-rts)
- [591b0aa](https://github.com/Surnet/swagger-jsdoc/commit/591b0aa1935cff4d728d903762f74833de387a40) Upgrades (@kalinchernev)
- [#180](https://github.com/Surnet/swagger-jsdoc/pull/180) Bump eslint-utils from 1.3.1 to 1.4.3 (#180) (@dependabot[bot])
- [#181](https://github.com/Surnet/swagger-jsdoc/pull/181) Bump lodash from 4.17.11 to 4.17.15 (#181) (@dependabot[bot])

### v3.4.0 (2019/08/08 08:59 +00:00)

- [80c350f](https://github.com/Surnet/swagger-jsdoc/commit/80c350f9cf3b91bc9f4f905c9b16ab920a588157) Release 3.4.0 (@kalinchernev)
- [#170](https://github.com/Surnet/swagger-jsdoc/pull/170) refactor(parser): expose specification builder methods (#170) (@gautier-lefebvre)

### v3.3.0 (2019/07/08 09:05 +00:00)

- [ce9ad85](https://github.com/Surnet/swagger-jsdoc/commit/ce9ad851f305b047c563163f436b89a9119025e0) Release 3.3.0 (@kalinchernev)
- [#166](https://github.com/Surnet/swagger-jsdoc/pull/166) chore(deps): upgrade all dependencies (#166) (@kalinchernev)
- [#165](https://github.com/Surnet/swagger-jsdoc/pull/165) Update swagger-parser to fix remote execution bug (#165) (@posquit0)
- [#164](https://github.com/Surnet/swagger-jsdoc/pull/164) Update outdated dev dependencies (#164) (@posquit0)
- [#162](https://github.com/Surnet/swagger-jsdoc/pull/162) fix: JSDoc Official website link (#162) (@katalonne)

### v3.2.9 (2019/04/16 06:42 +00:00)

- [ece24be](https://github.com/Surnet/swagger-jsdoc/commit/ece24be07458d9a4ff1b8a1020a4581624bb0f7c) Release 3.2.9 (@kalinchernev)
- [#157](https://github.com/Surnet/swagger-jsdoc/pull/157) Updating js-yaml dependency to 3.13.1 to fix remote execution vulnerability (#157) (@lerignoux)

### v3.2.8 (2019/03/22 06:40 +00:00)

- [098078b](https://github.com/Surnet/swagger-jsdoc/commit/098078b469eb0655fdb4143a0e99ea43b547e2f8) Release 3.2.8 (@kalinchernev)
- [#156](https://github.com/Surnet/swagger-jsdoc/pull/156) Bump js-yaml version to fix https://www.npmjs.com/advisories/788 (#156) (@iansltx)
- [#154](https://github.com/Surnet/swagger-jsdoc/pull/154) fix: CLI usage example (#154) (@GabrielDelepine)

### v3.2.7 (2019/02/12 14:18 +00:00)

- [9769dfd](https://github.com/Surnet/swagger-jsdoc/commit/9769dfd4326fb079bc9a0520ac30495909bf7e9e) Release 3.2.7 (@kalinchernev)
- [#151](https://github.com/Surnet/swagger-jsdoc/pull/151) Removes apis from input Def before generation. (#151) (@spencermcw)

### v3.2.6 (2018/11/30 16:09 +00:00)

- [3563890](https://github.com/Surnet/swagger-jsdoc/commit/35638903cd84c11b58d0f980b3e3aeb0ee70e22f) Release 3.2.6 (@kalinchernev)
- [#147](https://github.com/Surnet/swagger-jsdoc/pull/147) Adding specification configuration documentation to GETTING-STARTED.md (#147) (Mason Everett)

### v3.2.5 (2018/11/26 18:44 +00:00)

- [d0555ae](https://github.com/Surnet/swagger-jsdoc/commit/d0555ae18c0f0a5097695ef31db1b40f6f127530) Release 3.2.5 (@kalinchernev)
- [#145](https://github.com/Surnet/swagger-jsdoc/pull/145) fix: remove es2017 specific language feature (#145) (@a-morn)

### v3.2.4 (2018/11/23 10:31 +00:00)

- [714d42b](https://github.com/Surnet/swagger-jsdoc/commit/714d42b4539ec744e906f91aee399ac6995e9942) Release 3.2.4 (@kalinchernev)
- [#143](https://github.com/Surnet/swagger-jsdoc/pull/143) Return false for non-empty objects (#143) (@a-morn)
- [#140](https://github.com/Surnet/swagger-jsdoc/pull/140) Update CLI usage example (#140) (@ndelvalle)
- [#135](https://github.com/Surnet/swagger-jsdoc/pull/135) Quick spelling change (#135) (@antonjb)

### v3.2.3 (2018/09/19 06:50 +00:00)

- [36984bf](https://github.com/Surnet/swagger-jsdoc/commit/36984bf62c5f58e30e180176b6f9b68a4b451337) Correction (@kalinchernev)
- [2d2a6c7](https://github.com/Surnet/swagger-jsdoc/commit/2d2a6c791d968010fe66ed03c20a781df991675b) Release 3.2.2 (@kalinchernev)
- [e2e12fa](https://github.com/Surnet/swagger-jsdoc/commit/e2e12fa0153b8b627d910ed7f8508097951d6c02) Simplify (@kalinchernev)
- [36e2a48](https://github.com/Surnet/swagger-jsdoc/commit/36e2a48692a4db6252794d9a9c22984e7318d0fe) Add documentation section (@kalinchernev)

### v3.2.0 (2018/09/17 20:28 +00:00)

- [d4fc538](https://github.com/Surnet/swagger-jsdoc/commit/d4fc5386e53dc4168e998793045912a4b6ef371a) Release 3.2.0 (@kalinchernev)
- [#96](https://github.com/Surnet/swagger-jsdoc/pull/96) error reporting help with new "verbose" flag (#96) (@liquidg3)

### v3.1.1 (2018/09/16 15:47 +00:00)

- [fc5443e](https://github.com/Surnet/swagger-jsdoc/commit/fc5443e59b47f256128bb1c065909996097f5ddb) Release 3.1.1 (@kalinchernev)
- [9a3aaef](https://github.com/Surnet/swagger-jsdoc/commit/9a3aaef437256b88d3ccb0fff5fb94928c93de29) correction (@kalinchernev)
- [2c116a1](https://github.com/Surnet/swagger-jsdoc/commit/2c116a1ff4f1b0f502234939a9768b7f856da818) Add documentation (@kalinchernev)
- [aa7833d](https://github.com/Surnet/swagger-jsdoc/commit/aa7833dac699c504311839a0295c72af9c69e782) Add documentation (@kalinchernev)
- [a9d9bc6](https://github.com/Surnet/swagger-jsdoc/commit/a9d9bc685700374d3246784d894866c4d0c8628c) Add notes on relative paths (@kalinchernev)

### v3.1.0 (2018/09/16 09:49 +00:00)

- [6177568](https://github.com/Surnet/swagger-jsdoc/commit/6177568ea9888148d1ece64449e5274fa1d3bf3d) Release 3.1.0 (@kalinchernev)
- [#105](https://github.com/Surnet/swagger-jsdoc/pull/105) Feature/yaml input definition (#105) (@ehmicky)
- [4627728](https://github.com/Surnet/swagger-jsdoc/commit/462772861e2a69a27eda9dc5284a74a892cb6c0d) Update badge (@kalinchernev)
- [#131](https://github.com/Surnet/swagger-jsdoc/pull/131) refactor(helpers): remove unused functions (#131) (@kalinchernev)
- [#108](https://github.com/Surnet/swagger-jsdoc/pull/108) Allow YAML anchors (#108) (@ehmicky)

### 3.0.3 (2018/09/04 13:22 +00:00)

- [7b1d059](https://github.com/Surnet/swagger-jsdoc/commit/7b1d059da7c7ae63a9507f8b9a7ea4cd93b9ddab) Release 3.0.3 (@kalinchernev)
- [#130](https://github.com/Surnet/swagger-jsdoc/pull/130) chore(deps): upgrades (#130) (@kalinchernev)
- [#127](https://github.com/Surnet/swagger-jsdoc/pull/127) fix npm start, add env PORT as param (#127) (@Laboratory)

### 3.0.2 (2018/08/01 11:34 +00:00)

- [d676ba4](https://github.com/Surnet/swagger-jsdoc/commit/d676ba4553d16ee679ef9fac2ed80af28d574f92) Release 3.0.2 (same as 3.0.0, but npm cache issues) (@kalinchernev)
- [#122](https://github.com/Surnet/swagger-jsdoc/pull/122) OpenAPI support (#122) (@kalinchernev)
- [#121](https://github.com/Surnet/swagger-jsdoc/pull/121) Upgrades (#121) (@kalinchernev)

### v1.10.3 (2018/07/22 12:59 +00:00)

- [0da844f](https://github.com/Surnet/swagger-jsdoc/commit/0da844f98cdf2657546d898835530a84c3c7ec7b) Release 1.10.3 (@kalinchernev)
- [#115](https://github.com/Surnet/swagger-jsdoc/pull/115) Fix Issue #78 - Prevent paths overriding each other (#115) (@willvincent)
- [#109](https://github.com/Surnet/swagger-jsdoc/pull/109) Do not serialize YAML anchors (#109) (@ehmicky)

### 1.10.2 (2018/07/18 18:39 +00:00)

- [#120](https://github.com/Surnet/swagger-jsdoc/pull/120) Release 1.10.2 (#120) (@kalinchernev)
- [#119](https://github.com/Surnet/swagger-jsdoc/pull/119) Upgrade CircleCI to 2.0 (#119) (@kalinchernev)
- [#113](https://github.com/Surnet/swagger-jsdoc/pull/113) Update GETTING-STARTED.md (#113) (@hg-pyun)
- [#110](https://github.com/Surnet/swagger-jsdoc/pull/110) Upgrade chokidar 1.7.0 -> 2.0.3 (#110) (@ehmicky)
- [#118](https://github.com/Surnet/swagger-jsdoc/pull/118) Remove deprecated shield (#118) (@kalinchernev)
- [#97](https://github.com/Surnet/swagger-jsdoc/pull/97) Fix misleading description (#97) (@danielkhan)
- [#87](https://github.com/Surnet/swagger-jsdoc/pull/87) Fix apis name in CLI docs and error message (#87) (@sapegin)
- [#88](https://github.com/Surnet/swagger-jsdoc/pull/88) Add missed space (#88) (@sapegin)

### v1.9.7 (2017/07/24 17:11 +00:00)

- [#86](https://github.com/Surnet/swagger-jsdoc/pull/86) Bump to v1.9.7 (#86) (@drGrove)
- [#82](https://github.com/Surnet/swagger-jsdoc/pull/82) chore(deps): update dependencies and bump release (#82) (@kalinchernev)
- [#79](https://github.com/Surnet/swagger-jsdoc/pull/79) Remove unused swagger keys from swagger output object (#79) (@dolphub)
- [#77](https://github.com/Surnet/swagger-jsdoc/pull/77) chore(deps): update packages (#77) (@kalinchernev)
- [#72](https://github.com/Surnet/swagger-jsdoc/pull/72) Adding support for APIs in definition file. (#72) (Jesse O'Brien)

### v1.9.3 (2017/04/29 15:39 +00:00)

- [#69](https://github.com/Surnet/swagger-jsdoc/pull/69) docs(readme): improve documentation (#69) (@kalinchernev)
- [cd60d8a](https://github.com/Surnet/swagger-jsdoc/commit/cd60d8a5c54080a6ce7b43ce8932bc02aceae354) Removing Donation Link (@chdanielmueller)

### v1.9.2 (2017/02/22 13:05 +00:00)

- [6243281](https://github.com/Surnet/swagger-jsdoc/commit/6243281c0c8fbfc7e15a91f0a43f53915395606d) Release v.1.9.2 (@kalinchernev)
- [#59](https://github.com/Surnet/swagger-jsdoc/pull/59) Merge pull request #59 from Surnet/fix/dependencies (@Surnet)
- [b8fdd61](https://github.com/Surnet/swagger-jsdoc/commit/b8fdd6110628178f65f041583b881a4aee9f5b98) Moving chokidar dependency. (@kalinchernev)

### v1.9.1 (2017/01/22 11:26 +00:00)

- [50c3a2c](https://github.com/Surnet/swagger-jsdoc/commit/50c3a2c1842a243cd8d2a4245077c417c6d78e7e) Release v1.9.1 (@kalinchernev)
- [#55](https://github.com/Surnet/swagger-jsdoc/pull/55) Merge pull request #55 from Surnet/chore/npmignore (@Surnet)
- [3e9a2b2](https://github.com/Surnet/swagger-jsdoc/commit/3e9a2b27ed88d37e6de1d687d574e15455f927f5) Ignore c9 hidden folder on npm publish (@kalinchernev)
- [d7deb0b](https://github.com/Surnet/swagger-jsdoc/commit/d7deb0bd723eedfd77b899e0b3126ddf324f1486) Default output file is actually swagger.json (@kalinchernev)

### v1.9.0 (2017/01/17 09:25 +00:00)

- [ee44bb2](https://github.com/Surnet/swagger-jsdoc/commit/ee44bb2f3893f57c7b31d444caf48248862ad585) Release v1.9.0 (@kalinchernev)
- [#49](https://github.com/Surnet/swagger-jsdoc/pull/49) Merge pull request #49 from Surnet/feature/watch-task (@Surnet)
- [6993f69](https://github.com/Surnet/swagger-jsdoc/commit/6993f69586640ea87b4d354fcd540f837b20f22f) Update tests (@kalinchernev)
- [7117fd8](https://github.com/Surnet/swagger-jsdoc/commit/7117fd89b507c88f48edf7873b2ec474d974a6e9) Update to latest master (@kalinchernev)
- [#53](https://github.com/Surnet/swagger-jsdoc/pull/53) Merge pull request #53 from mandrean/feat/yaml-output (@mandrean)
- [f249ee4](https://github.com/Surnet/swagger-jsdoc/commit/f249ee4f249f93cbe2676fbdeb0cee19137c2234) Remove extra space (@kalinchernev)
- [87527c2](https://github.com/Surnet/swagger-jsdoc/commit/87527c26a515cbf78ce06d3cccfcf6353e43fb9a) Turn off coverage requirements for example code (@kalinchernev)
- [5d4e599](https://github.com/Surnet/swagger-jsdoc/commit/5d4e599d424cd472bfd3636afaaf78eaff3d373e) Avoid tags duplication (@kalinchernev)
- [8986e85](https://github.com/Surnet/swagger-jsdoc/commit/8986e8549b0cf1b71052b10e16ae01a86e363b98) Add CLI support for YAML output (@mandrean)
- [103aab6](https://github.com/Surnet/swagger-jsdoc/commit/103aab6f5df92b87c2c951475471b64794078ada) Update documentation of CLI tool (@kalinchernev)
- [ccfa0f9](https://github.com/Surnet/swagger-jsdoc/commit/ccfa0f9a4c842d70765d607459fe41047937136b) Watch only files with API documentation (@kalinchernev)
- [5713535](https://github.com/Surnet/swagger-jsdoc/commit/571353584e065e24cc3c28c980c4972c1c527f60) Improve cli watch task (@kalinchernev)
- [fd16962](https://github.com/Surnet/swagger-jsdoc/commit/fd16962d7f1c0bf075ab77b0ca2519ca747be923) Include watch task in cli tool. (@kalinchernev)

### v1.8.4 (2016/12/29 12:04 +00:00)

- [20b9516](https://github.com/Surnet/swagger-jsdoc/commit/20b951663cec24dc44768c42ac92443e57921350) Release v1.8.4 (@kalinchernev)
- [#45](https://github.com/Surnet/swagger-jsdoc/pull/45) Merge pull request #45 from toefraz/master (@toefraz)
- [5ff7718](https://github.com/Surnet/swagger-jsdoc/commit/5ff77186dc1e6ba5dba96ed6f4b0cc782ec00300) Adding tests to verify
- [e0bcb38](https://github.com/Surnet/swagger-jsdoc/commit/e0bcb38ccb15c1fc50306026ad1209caaf579633) Ignore deprecation check (@toefraz)

### v1.8.3 (2016/12/04 13:23 +00:00)

- [d777cde](https://github.com/Surnet/swagger-jsdoc/commit/d777cde8e0e960820e5088ee18739a318ecd8ed4) Release v1.8.3 (@kalinchernev)
- [#43](https://github.com/Surnet/swagger-jsdoc/pull/43) Merge pull request #43 from Surnet/chore/dependencies (@Surnet)
- [ce3859f](https://github.com/Surnet/swagger-jsdoc/commit/ce3859fcc6cf5d56b349b04e073a34509d7ec95a) chore(dependencies): update modules (@kalinchernev)

### v1.8.2 (2016/11/18 19:08 +00:00)

- [44e4168](https://github.com/Surnet/swagger-jsdoc/commit/44e41683849d43edad1bd28b392913409d0744c2) Release v1.8.2 (@kalinchernev)
- [#42](https://github.com/Surnet/swagger-jsdoc/pull/42) Merge pull request #42 from cikasfm/master (@cikasfm)
- [a85c4b8](https://github.com/Surnet/swagger-jsdoc/commit/a85c4b8eeff3c424bb52569f1cd13cd87f36b64b) Updating sample to comply with Swagger v2 (@cikasfm)

### v1.8.1 (2016/10/27 11:55 +00:00)

- [dd1aa0a](https://github.com/Surnet/swagger-jsdoc/commit/dd1aa0a21f34b3be6c0215dddcb6f5394847eb09) Release v1.8.1 (@kalinchernev)
- [eaa1e80](https://github.com/Surnet/swagger-jsdoc/commit/eaa1e804fa97748765008185914f7ef7680fcd2b) Release v1.7.0 (@kalinchernev)
- [#40](https://github.com/Surnet/swagger-jsdoc/pull/40) Merge pull request #40 from Surnet/feature/swagger-spec-revision (@Surnet)
- [50c99c0](https://github.com/Surnet/swagger-jsdoc/commit/50c99c02df376a18d508409709a168a823289681) add iterator to seek for problematic tags (@kalinchernev)
- [1d27a43](https://github.com/Surnet/swagger-jsdoc/commit/1d27a4328843b53d91b9eadfc53951c809308973) include seekWrong method (@kalinchernev)
- [b0e73f1](https://github.com/Surnet/swagger-jsdoc/commit/b0e73f117fac637cbd3271a023ad59611dde8dd3) Preparing for general reporting of deprecated properties. (@kalinchernev)
- [4f3641d](https://github.com/Surnet/swagger-jsdoc/commit/4f3641d1fd24b4a5516d8bbb11a2829e4fb10051) Including checks for securityDefinitions and responses (@kalinchernev)
- [87f6cf5](https://github.com/Surnet/swagger-jsdoc/commit/87f6cf52cc2244ff5ec3b84e1a7e41b68c553d40) Adding test for accepting parameter in singular and plural (@kalinchernev)
- [70f924d](https://github.com/Surnet/swagger-jsdoc/commit/70f924d48128e26551c9170a1cb4ceeff3454502) Updating example files (@kalinchernev)
- [6360c1d](https://github.com/Surnet/swagger-jsdoc/commit/6360c1d1947f92109b1d5abb240a1ee6077fa292) Reducing function cyclomatic complexity (@kalinchernev)
- [ae4e4b8](https://github.com/Surnet/swagger-jsdoc/commit/ae4e4b8bcb54c21f5e32e1ddd7e38d9d1248618b) Separating the switch. (@kalinchernev)
- [f204dde](https://github.com/Surnet/swagger-jsdoc/commit/f204ddec6b8a788f0ab1c9e3a60b824f983c90a0) Correcting definition. (@kalinchernev)
- [5c55c1e](https://github.com/Surnet/swagger-jsdoc/commit/5c55c1e8e7fda9a855de070396e52e0268a8ef4e) End of day commit. (@kalinchernev)
- [ff58ba0](https://github.com/Surnet/swagger-jsdoc/commit/ff58ba059b5c6f0342bbc9e7e5a4997b472137db) addDataToSwaggerObject requires parameters. (@kalinchernev)
- [0579d90](https://github.com/Surnet/swagger-jsdoc/commit/0579d903d3f33aa83df17b10de6ed0f31a76a5e7) Separating swagger related functions in a module. (@kalinchernev)

### v1.7.0 (2016/09/17 17:33 +00:00)

- [973e6b0](https://github.com/Surnet/swagger-jsdoc/commit/973e6b0dab2f5c0b30facadd14a2ce580d9cd48b) Bump to v1.7.0 (@drGrove)
- [#36](https://github.com/Surnet/swagger-jsdoc/pull/36) Tags property parsing refactoring (#36) (@kalinchernev)

### v1.6.0 (2016/09/02 05:30 +00:00)

- [c809ffc](https://github.com/Surnet/swagger-jsdoc/commit/c809ffc075c06c482d4d8d88991aa4fb552326cd) Bump to v1.6.0 (@drGrove)
- [#29](https://github.com/Surnet/swagger-jsdoc/pull/29) Don't override user provided swagger definition options (#29) (@brantw)
- [#30](https://github.com/Surnet/swagger-jsdoc/pull/30) support multiple tags as an array and in definition (#30) (@efmr)
- [#31](https://github.com/Surnet/swagger-jsdoc/pull/31) CLI should use package version (#31) (@drGrove)

### v1.5.0 (2016/08/30 07:13 +00:00)

- [3891ee2](https://github.com/Surnet/swagger-jsdoc/commit/3891ee2c96513009efe901ee60a45ccb84994793) Bump to v1.5.0 (@drGrove)

### v1.4.1 (2016/08/30 07:10 +00:00)

- [0752561](https://github.com/Surnet/swagger-jsdoc/commit/0752561610ada57430204df88f8c043c8362ab67) Bump to v1.4.1 (@drGrove)
- [#27](https://github.com/Surnet/swagger-jsdoc/pull/27) CLI (#27) (@kalinchernev)

### v1.4.0 (2016/08/25 08:52 +00:00)

- [72ae1bb](https://github.com/Surnet/swagger-jsdoc/commit/72ae1bbae07ebd3c9d503fe1a7942efdf1e3e7b4) Bump to v1.4.0 (@drGrove)

### v1.3.1 (2016/07/03 18:12 +00:00)

- [3cccf49](https://github.com/Surnet/swagger-jsdoc/commit/3cccf498ed70c0d8240fea045156f1d837fa928f) Version bump (@chdanielmueller)
- [#23](https://github.com/Surnet/swagger-jsdoc/pull/23) Merge pull request #23 from simast/master (@simast)
- [f01b48b](https://github.com/Surnet/swagger-jsdoc/commit/f01b48b2b14a1f6be0a04a0fa4e8d6ce72b96aa4) Added swagger tags support.
- [e6d3eec](https://github.com/Surnet/swagger-jsdoc/commit/e6d3eec1ac1b3b2239f983a3049ece6b990637c8) Update README.md (@chdanielmueller)

### v1.3.0 (2016/04/07 14:10 +00:00)

- [#20](https://github.com/Surnet/swagger-jsdoc/pull/20) Merge pull request #20 from jonboiser/master (@jonboiser)
- [0af0c5d](https://github.com/Surnet/swagger-jsdoc/commit/0af0c5d7f16f1442cea5aa8c1f33c13913197016) Update to version 1.3.0
- [257bdd3](https://github.com/Surnet/swagger-jsdoc/commit/257bdd380250fe0b397521989e272b44727eb1c3) Remove Login2 definition.
- [1cb5fa0](https://github.com/Surnet/swagger-jsdoc/commit/1cb5fa04825fb2b413395d3ed8bc14f7748c96e5) Create test case for using globs options.apis.
- [2753f8e](https://github.com/Surnet/swagger-jsdoc/commit/2753f8edbd17e1a2b3eec18eae4d8bea79d4e8d7) Factor out glob converter function.
- [e73de29](https://github.com/Surnet/swagger-jsdoc/commit/e73de292abcc3564203a4cfe49f75285101f50ff) Raise jshint maxstatements to 20.
- [2e2ef7c](https://github.com/Surnet/swagger-jsdoc/commit/2e2ef7cdb140befee5d60909dc603fca53bb8ae0) options.api accepts glob strings

### v1.2.1 (2016/03/12 14:09 +00:00)

- [e42fb0c](https://github.com/Surnet/swagger-jsdoc/commit/e42fb0c77835b18e825b832be6622b4f4052d6dd) Update version number, change node test version (@chdanielmueller)
- [#18](https://github.com/Surnet/swagger-jsdoc/pull/18) Merge pull request #18 from mprokopowicz/master (@mprokopowicz)
- [78e7b77](https://github.com/Surnet/swagger-jsdoc/commit/78e7b77145ace4fe8c33d29929638c4c99d14c3e) use path.extname in favor of path.parse to stay compatible with node 0.10.x (@mprokopowicz)

### v1.2.0 (2016/02/26 18:52 +00:00)

- [0b7fe37](https://github.com/Surnet/swagger-jsdoc/commit/0b7fe379a30cc8eeb9391044de7ccfd2f563ed80) Updating version number and dependencies (@chdanielmueller)
- [#15](https://github.com/Surnet/swagger-jsdoc/pull/15) Merge pull request #15 from Cloudoki/master (@Cloudoki)
- [f31413a](https://github.com/Surnet/swagger-jsdoc/commit/f31413ae1a866e0f084dd0070b86c39d2e3b46f3) remove console.log and add forgotten file (@efmr)
- [3e60da2](https://github.com/Surnet/swagger-jsdoc/commit/3e60da2f52c846e627ecf6a992346d42b1465d26) accept yaml as api docs (@efmr)
- [4d7da54](https://github.com/Surnet/swagger-jsdoc/commit/4d7da54d2aee0ebeb7f1c0cc20924815ce8038c3) pass lint and test (@efmr)
- [20b95f2](https://github.com/Surnet/swagger-jsdoc/commit/20b95f280f8ca65776260dcfb714b107989a0d38) add parameters, responses, securityDefinitions (@efmr)
- [#13](https://github.com/Surnet/swagger-jsdoc/pull/13) Merge pull request #13 from ami44/master (@ami44)
- [863a3bb](https://github.com/Surnet/swagger-jsdoc/commit/863a3bb3c84a2f5a1ba68be781dd92d2c4f9551b) Add example to load external definitions

### v1.1.2 (2015/11/25 10:06 +00:00)

- [fb86b3d](https://github.com/Surnet/swagger-jsdoc/commit/fb86b3d39ce1df0b81f2c0cc8912f44026a2a7cc) Update version number to 1.1.2 (@chdanielmueller)
- [#11](https://github.com/Surnet/swagger-jsdoc/pull/11) Merge pull request #11 from trendfischer/master (@trendfischer)
- [b1695c0](https://github.com/Surnet/swagger-jsdoc/commit/b1695c033d8f5dc8274450214cfa5737f8decba8) Removed required path module (@trendfischer)
- [ec1de9c](https://github.com/Surnet/swagger-jsdoc/commit/ec1de9c80814296ad2ebd34c78a0e18113834bdb) Removed filename restriction for "\*.js" (@trendfischer)

### v1.1.1 (2015/09/27 17:07 +00:00)

- [afe96f0](https://github.com/Surnet/swagger-jsdoc/commit/afe96f063e5cd206d22ce724e27643d99b47a4f9) Updating dependencies (@chdanielmueller)
- [#10](https://github.com/Surnet/swagger-jsdoc/pull/10) Merge pull request #10 from drGrove/docs/definition (@drGrove)
- [c77cd0e](https://github.com/Surnet/swagger-jsdoc/commit/c77cd0ec2ab886ad711ad014a6037a8400111907) Adds docs for definition creation (@drGrove)

### v1.1.0 (2015/08/30 15:20 +00:00)

- [511778e](https://github.com/Surnet/swagger-jsdoc/commit/511778e506ec32f1b141273ec43fcacb6f390abd) Updating version number and fixing spelling error (@chdanielmueller)
- [#8](https://github.com/Surnet/swagger-jsdoc/pull/8) Merge pull request #8 from drGrove/feature/definitions (@drGrove)
- [f264e0e](https://github.com/Surnet/swagger-jsdoc/commit/f264e0e3e0a3aeea7e89cf29b5f7ff675a5ec318) Adds editorconfig (@drGrove)
- [6a512b1](https://github.com/Surnet/swagger-jsdoc/commit/6a512b182cd332ede62be2b020c30ad142508c53) Adds support for external definitions. Updates maxstatements to 15 (@drGrove)

### v1.0.1 (2015/08/23 08:25 +00:00)

- [359f239](https://github.com/Surnet/swagger-jsdoc/commit/359f239a85e87f7fc3cdbeead98e52b4ae257e99) Update version number (@chdanielmueller)
- [#6](https://github.com/Surnet/swagger-jsdoc/pull/6) Merge pull request #6 from drGrove/master (@drGrove)
- [0f7a70c](https://github.com/Surnet/swagger-jsdoc/commit/0f7a70cc7a8bdbf58224eb91ed54b57f7cbe6ca4) Adds parser for separate definitions (@drGrove)
- [7b88757](https://github.com/Surnet/swagger-jsdoc/commit/7b887578496c05f572b8b86fe879e48e7e05fb99) Removes console.logs (@drGrove)
- [07e40ad](https://github.com/Surnet/swagger-jsdoc/commit/07e40ada1da9852e7d34f94301b5ec6e06d41045) Adds object merging to allow for swagger docs of same endpoint with different methods [GET,POST] to be associated with the endpoint (@drGrove)
- [#5](https://github.com/Surnet/swagger-jsdoc/pull/5) Merge pull request #5 from chdanielmueller/master (@chdanielmueller)
- [ccd119e](https://github.com/Surnet/swagger-jsdoc/commit/ccd119e25a9730d0b1f95be74ef30d2aa82a870c) More documentation (@chdanielmueller)
- [#4](https://github.com/Surnet/swagger-jsdoc/pull/4) Merge pull request #4 from chdanielmueller/master (@chdanielmueller)
- [b0e7534](https://github.com/Surnet/swagger-jsdoc/commit/b0e75343183576694087d568dd3909cf4db8884b) Adding npm and Gratipay badges (@chdanielmueller)

### v1.0.0 (2015/06/09 16:33 +00:00)

- [#3](https://github.com/Surnet/swagger-jsdoc/pull/3) Merge pull request #3 from chdanielmueller/master (@chdanielmueller)
- [62bd690](https://github.com/Surnet/swagger-jsdoc/commit/62bd69001a28bce0c611a63cd9dc38cff5ff810d) Update to version 1.0.0 (@chdanielmueller)
- [3077045](https://github.com/Surnet/swagger-jsdoc/commit/30770457149f205433ff88999ea9fcaa17a99676) README.md (@chdanielmueller)
- [1ddc012](https://github.com/Surnet/swagger-jsdoc/commit/1ddc0121ecfae783b7242d3a82c39cdb2aa68a50) Remove swagger-tools (@chdanielmueller)
- [f63a68b](https://github.com/Surnet/swagger-jsdoc/commit/f63a68bdbe172ea6d17bdb10e3479d69cb82a41e) Exclude swagger serving from library (@chdanielmueller)
- [d6fa533](https://github.com/Surnet/swagger-jsdoc/commit/d6fa5331c18b3f0dd5cf3ea0255ecb8d026d93c1) Clean swagger spec in example (@chdanielmueller)
- [#2](https://github.com/Surnet/swagger-jsdoc/pull/2) Merge pull request #2 from chdanielmueller/master (@chdanielmueller)
- [4c0a6e0](https://github.com/Surnet/swagger-jsdoc/commit/4c0a6e0232c080624d4a3b34bb23f4a4a56389af) Fixing circle.yml (@chdanielmueller)
- [96e12c7](https://github.com/Surnet/swagger-jsdoc/commit/96e12c7fdcedb99176258d497509b24e1f011bae) Adding Badges to README.md (@chdanielmueller)
- [3fa16f2](https://github.com/Surnet/swagger-jsdoc/commit/3fa16f259c118a935652b28d55edc835f5d5b94f) Adding Codacy Coverage Token (@chdanielmueller)
- [64b3121](https://github.com/Surnet/swagger-jsdoc/commit/64b312185d123e9005fef988cd0a521bed5eb6c0) Typo (@chdanielmueller)
- [87e1730](https://github.com/Surnet/swagger-jsdoc/commit/87e1730f2919e2b01dae3f92885bbae41e56e312) Allow more statements (@chdanielmueller)
- [e81460e](https://github.com/Surnet/swagger-jsdoc/commit/e81460e5e0179f289c445362c1652543577a0247) Increasing coverage (@chdanielmueller)
- [f3449bb](https://github.com/Surnet/swagger-jsdoc/commit/f3449bba8786cabeaf97f63e1426a2784e1c8914) Refactoring (@chdanielmueller)
- [617a73f](https://github.com/Surnet/swagger-jsdoc/commit/617a73f6bf962dcfadde6308a1f9f2c7157625e7) Adding tests (@chdanielmueller)
- [2d2ecfe](https://github.com/Surnet/swagger-jsdoc/commit/2d2ecfec3db7d47bc7b62236d16f7b03d0078a8a) Prepare for CI (@chdanielmueller)
- [fea32b5](https://github.com/Surnet/swagger-jsdoc/commit/fea32b57e590c86d1bd2e4c74ba71182bb6645c0) Cleanup README.md (@chdanielmueller)
- [535b66d](https://github.com/Surnet/swagger-jsdoc/commit/535b66d7710d085ab44ba4463d18d217ca70b099) Adding references to external dependencies (@chdanielmueller)
- [c99b300](https://github.com/Surnet/swagger-jsdoc/commit/c99b3008f726dbe6d8bb14ff0f118aeb7737006c) Remove jsdoc from example (@chdanielmueller)
- [365f12b](https://github.com/Surnet/swagger-jsdoc/commit/365f12b25a842b9020bc21b5a1e3aaa77d26144e) Fix jscs and jshint errors (@chdanielmueller)
- [d7d6964](https://github.com/Surnet/swagger-jsdoc/commit/d7d6964762430efa1dc0fa00e9a8ee517747f92e) Adding coverage test (@chdanielmueller)
- [e3f6c3f](https://github.com/Surnet/swagger-jsdoc/commit/e3f6c3f7ac74cd4d764d17018430227c1b8170fa) Tweaks to the package.json (@chdanielmueller)
- [5a68333](https://github.com/Surnet/swagger-jsdoc/commit/5a68333b518d0dee9e3b48d10d2184fee82bbcc1) Remove example files (@chdanielmueller)
- [0df567c](https://github.com/Surnet/swagger-jsdoc/commit/0df567cdc826e1b4d5bae09795afe79cb5211cb2) Rename project, change URLs, Fix LICENSE (@chdanielmueller)
- [1562666](https://github.com/Surnet/swagger-jsdoc/commit/1562666e05efd4ed38af080183c946a2849fae45) Adding tests (@chdanielmueller)
- [e2768ac](https://github.com/Surnet/swagger-jsdoc/commit/e2768aca5117363a102161f00fd85d8b0b0c3d7d) Add jsdoc config (@chdanielmueller)
- [79a0758](https://github.com/Surnet/swagger-jsdoc/commit/79a0758a7410868533e9366390f6d1631d66df3a) Implement jscs and jshint (@chdanielmueller)
- [#1](https://github.com/Surnet/swagger-jsdoc/pull/1) Merge pull request #1 from chdanielmueller/refactor (@chdanielmueller)
- [5313d40](https://github.com/Surnet/swagger-jsdoc/commit/5313d40cef4e96430ec039eb8dd81b03a0b8b367) Working login example (@chdanielmueller)
- [a6b81b6](https://github.com/Surnet/swagger-jsdoc/commit/a6b81b69279cb15191eba84eb6d012a68b106c3a) Adding swaggerDefinition (@chdanielmueller)
- [c80bb01](https://github.com/Surnet/swagger-jsdoc/commit/c80bb0157dc642fb70bff8c51e7069c22ee0bf22) Switch to simple PetStore example (@chdanielmueller)
- [e807d99](https://github.com/Surnet/swagger-jsdoc/commit/e807d9917b7681e139d2825bcdf90086f4ca523d) Adding swagger-example in json and yml (@chdanielmueller)
- [824d1c1](https://github.com/Surnet/swagger-jsdoc/commit/824d1c155d956371d23577712bcb396c2a7769f2) Beautify Code (@chdanielmueller)
- [afed184](https://github.com/Surnet/swagger-jsdoc/commit/afed184047de4d4ea8dffc3b59e3ccbf5a9c9c1f) Remove express as dependency (@chdanielmueller)
- [0051b73](https://github.com/Surnet/swagger-jsdoc/commit/0051b73b9156ab66f3611a1d4b560fd0b54d9dd7) Change Formatting (@chdanielmueller)
- [5cd7b15](https://github.com/Surnet/swagger-jsdoc/commit/5cd7b15628b145c61ba4cf6b01caa356c2c3bcb6) Remove unneeded swaggerUI (@chdanielmueller)
- [dffb84a](https://github.com/Surnet/swagger-jsdoc/commit/dffb84ad40da37a1c0098a289381b35be1cab5e7) Modifiable URLs for documentation and swaggerUI (@chdanielmueller)
- [ebf2d72](https://github.com/Surnet/swagger-jsdoc/commit/ebf2d72c4c880cead651dceac51f2801dfd5ab4b) Adding swagger-tools (@chdanielmueller)
- [5b4c1eb](https://github.com/Surnet/swagger-jsdoc/commit/5b4c1eb1a408ab665e38b008a441e8ced792e528) It works!!! Now I have to refactor and do some tweaks. (@devlouisc)
- [d850f3f](https://github.com/Surnet/swagger-jsdoc/commit/d850f3f5112bc76f87b046003046a5783c5d97e6) Fix listing issues (@devlouisc)
- [7a82b46](https://github.com/Surnet/swagger-jsdoc/commit/7a82b46d468267c96b2cc9e04b3af96dce8fabfc) Remove example directory from ESLint ignore (@devlouisc)
- [fff57be](https://github.com/Surnet/swagger-jsdoc/commit/fff57be848f7e061ef1e9971bef1c589c43af095) Clean up comments (@devlouisc)
- [e65e356](https://github.com/Surnet/swagger-jsdoc/commit/e65e356e4d99cfb09cac96e4eb6cd475c17406ad) Add beginnings of new example app (@devlouisc)
- [f05a51f](https://github.com/Surnet/swagger-jsdoc/commit/f05a51fc1a0c5556971c3db2a62729bc1966391b) Fix linting (@devlouisc)
- [a98b8ab](https://github.com/Surnet/swagger-jsdoc/commit/a98b8aba143138b46bdb2f98722f7be588607b9f) Reorganize files (@devlouisc)
- [51ac313](https://github.com/Surnet/swagger-jsdoc/commit/51ac31387d8336e1962bf5fce1abb57f17050406) Update metadata with new project name and trim dependencies (@devlouisc)
- [8c86b95](https://github.com/Surnet/swagger-jsdoc/commit/8c86b95529e075db8392856a78668c978ede07c3) Remove support for YAML and CoffeeScript (@devlouisc)
- [e0df0ed](https://github.com/Surnet/swagger-jsdoc/commit/e0df0edd0f14e9c8f310ca203ccb7f8729cb460c) Remove old implementation (@devlouisc)
- [00f3be7](https://github.com/Surnet/swagger-jsdoc/commit/00f3be7c554f0fd4a09841ba4b9e4df3a5d6b296) Fix listing problems (@devlouisc)
- [0ba898b](https://github.com/Surnet/swagger-jsdoc/commit/0ba898bab5c37d97c88c39bf16431b8e4ebe92ed) Finish implementation of logic (@devlouisc)
- [cc1fedd](https://github.com/Surnet/swagger-jsdoc/commit/cc1feddff1be165e59a883b5ed56a4783e8a206c) Add example Swagger 2.0 JSON object (@devlouisc)
- [62eae1d](https://github.com/Surnet/swagger-jsdoc/commit/62eae1d32d8e684a7053e862f732dad8045be4b0) Add documentation and beginnings of parsing logic (@devlouisc)
- [465d359](https://github.com/Surnet/swagger-jsdoc/commit/465d3594eb2c2511ca3954b24506376b9d4314b4) Fine tune linting (@devlouisc)
- [bd2b1b4](https://github.com/Surnet/swagger-jsdoc/commit/bd2b1b4379744bea298da2c1e7d1b55322451460) Add and configure ESLint (@devlouisc)
- [e88fc7a](https://github.com/Surnet/swagger-jsdoc/commit/e88fc7a653ebd489f8f8027da29930d87e2d2f84) Add Swagger UI (@devlouisc)
- [95bd90f](https://github.com/Surnet/swagger-jsdoc/commit/95bd90f308ffcdd9468cba34e90e65d0cf08d7c1) Add options validation (@devlouisc)
- [33a1f98](https://github.com/Surnet/swagger-jsdoc/commit/33a1f980a22015dd70d543ecc1ad58898a0c9cb0) Update license (@devlouisc)
- [1160f1d](https://github.com/Surnet/swagger-jsdoc/commit/1160f1d477f9b6febab8d0e3032925b98a0f8408) Update npm dependency references (@devlouisc)
- [3f54b0d](https://github.com/Surnet/swagger-jsdoc/commit/3f54b0dbba7538c12cafff6fe748debfc62a4edc) Remove ignored files that do not pertain to my system (@devlouisc)
- [8031793](https://github.com/Surnet/swagger-jsdoc/commit/803179308f26e9345b32dabde87432158f8717c8) Remove global flag from npm install (@devlouisc)
- [a75ff89](https://github.com/Surnet/swagger-jsdoc/commit/a75ff894b22f3f2eb37f9c76bbc07261b109ff11) Fix formatting of comments (@devlouisc)
- [ca5e5b2](https://github.com/Surnet/swagger-jsdoc/commit/ca5e5b2770bb715b1e3a24829a48c0d22f9b4a4d) Format and alphabetize text (@devlouisc)
- [#25](https://github.com/Surnet/swagger-jsdoc/pull/25) Merge pull request #25 from miltonguty/patch-1 (@miltonguty)
- [8e6f862](https://github.com/Surnet/swagger-jsdoc/commit/8e6f862bc350afa2b979e21444b4063ee911d522) enable "Access-Control-Allow-Origin" in request (@miltonguty)
- [#24](https://github.com/Surnet/swagger-jsdoc/pull/24) Merge pull request #24 from relvao/master (@relvao)
- [e61fcbd](https://github.com/Surnet/swagger-jsdoc/commit/e61fcbde87822767077f14681a7db03e1fabfeab) fix Express 4 warning (@relvao)
- [#21](https://github.com/Surnet/swagger-jsdoc/pull/21) Merge pull request #21 from ElectricHummingbird/master (@ElectricHummingbird)
- [4ef14e4](https://github.com/Surnet/swagger-jsdoc/commit/4ef14e4ac0cc70d3b98c474f5e32f7fb43be9959) allow fullSwaggerJSONPath to be set in the cfg.
- [#18](https://github.com/Surnet/swagger-jsdoc/pull/18) Merge pull request #18 from tlvince/refactor/jshint (@tlvince)
- [#17](https://github.com/Surnet/swagger-jsdoc/pull/17) Merge pull request #17 from tlvince/feature/info (@tlvince)
- [388396c](https://github.com/Surnet/swagger-jsdoc/commit/388396ccd843854c56d3c5bc82d29dd115ca841c) Pass info object (@tlvince)
- [7aa9e99](https://github.com/Surnet/swagger-jsdoc/commit/7aa9e990cbb304493373e8ea0e0abfbd8d6a3d9d) Fix JSHint warning (@tlvince)
- [#16](https://github.com/Surnet/swagger-jsdoc/pull/16) Merge pull request #16 from gierschv/feat-expose (@gierschv)
- [f136d74](https://github.com/Surnet/swagger-jsdoc/commit/f136d74346eaf4fede718628e8285bd105cf09f8) Expose descriptor & resources (@gierschv)
- [#15](https://github.com/Surnet/swagger-jsdoc/pull/15) Merge pull request #15 from relvao/master (@relvao)
- [6190d11](https://github.com/Surnet/swagger-jsdoc/commit/6190d11d03c2361504fd48085081f21f147cbe24) Update index.js (@relvao)
- [#12](https://github.com/Surnet/swagger-jsdoc/pull/12) Merge pull request #12 from sposmen/master (@sposmen)
- [#13](https://github.com/Surnet/swagger-jsdoc/pull/13) Merge pull request #13 from johnywith1n/master (@johnywith1n)
- [0839ab0](https://github.com/Surnet/swagger-jsdoc/commit/0839ab00fcce601b201f94daf752ec5573cf7c3b) remove doc about basePath being optional.
- [bc2fba2](https://github.com/Surnet/swagger-jsdoc/commit/bc2fba285c16e75a165d6425d46951c03be199ee) set version of express to 3.5.1 since v4.0.0 doesnt work with the example app.
- [d8c80f3](https://github.com/Surnet/swagger-jsdoc/commit/d8c80f38b50a2787e440a3678bc973264d8cf845) require base path. match swagger json endpoints based on the full path which may include any parts from the basePath after the host.
- [32b28b2](https://github.com/Surnet/swagger-jsdoc/commit/32b28b205a42851c370c2df54218bf2f5aa731ba) Middleware function support (@sposmen)
- [#10](https://github.com/Surnet/swagger-jsdoc/pull/10) Merge pull request #10 from sposmen/master (@sposmen)
- [7c15437](https://github.com/Surnet/swagger-jsdoc/commit/7c1543719490b3b733eba7ae7ccd4c5392059cd2) Merge branch 'master' of github.com:sposmen/swagger-express (@sposmen)
- [36e27fd](https://github.com/Surnet/swagger-jsdoc/commit/36e27fd901b9fcf9be7f75fb1618953391e81724) README documentantion complement (@sposmen)
- [5e022e6](https://github.com/Surnet/swagger-jsdoc/commit/5e022e60ce1a4ff2df343893924cbe67b8c230f7) README documentantion complement (@sposmen)
- [1aebc40](https://github.com/Surnet/swagger-jsdoc/commit/1aebc40ea9e00134bc2961744387e060810fa8d3) New Jade standard (avoiding errors) (@sposmen)
- [5768a3d](https://github.com/Surnet/swagger-jsdoc/commit/5768a3daca8cee79d30b51025aa535d553d9b149) Specific origin of examples (@sposmen)
- [e528cd2](https://github.com/Surnet/swagger-jsdoc/commit/e528cd2490ada38d3013539d11607f718dd9342d) Coffee support added (@sposmen)
- [e38c3b9](https://github.com/Surnet/swagger-jsdoc/commit/e38c3b947071c85ee73c16113035ab164dd065a6) update version (@fliptoo)
- [14ccf0b](https://github.com/Surnet/swagger-jsdoc/commit/14ccf0b7effc2379f1b7adbe440b70c858f23aee) fixed swagger option (@fliptoo)
- [#6](https://github.com/Surnet/swagger-jsdoc/pull/6) Merge pull request #6 from slajax/master (@slajax)
- [9623f70](https://github.com/Surnet/swagger-jsdoc/commit/9623f704aa0a76824357314de81c2c02c5889549) added basePath to readme (@kc-dot-io)
- [1b4f3d6](https://github.com/Surnet/swagger-jsdoc/commit/1b4f3d6dfb2ba93e9d5e673083db2dbe9e186bad) basePath should still be able to be set so it can be passed to swagger.js (@kc-dot-io)
- [e97684d](https://github.com/Surnet/swagger-jsdoc/commit/e97684da334d7fd7414e87505de39c6e8336553f) Fixes #4 (@fliptoo)
- [9e89f97](https://github.com/Surnet/swagger-jsdoc/commit/9e89f977d831650ae5392a3a89277d4e43a5ecae) Update Example (@fliptoo)
- [4581d7a](https://github.com/Surnet/swagger-jsdoc/commit/4581d7a8f0fe05c10a1b7323f586b9091cade748) Fixes #4 (@fliptoo)
- [d481656](https://github.com/Surnet/swagger-jsdoc/commit/d4816562346ef030844998804c1ae754e3fc6fa6) Update Version (@fliptoo)
- [96262f9](https://github.com/Surnet/swagger-jsdoc/commit/96262f9a12ddb8ef9b79c2fd6084e7669b011199) Update Example (@fliptoo)
- [6e29773](https://github.com/Surnet/swagger-jsdoc/commit/6e29773ddd69351f50d21848d7507926a1027384) Set version on js-yaml to suppress deprecation warning output. (@fliptoo)
- [#3](https://github.com/Surnet/swagger-jsdoc/pull/3) Merge pull request #3 from calmdev/master (@calmdev)
- [39ca987](https://github.com/Surnet/swagger-jsdoc/commit/39ca987997c75c3b984cc3534ecdfcd9b0b62435) Updates the README file. (@calmdev)
- [cf31676](https://github.com/Surnet/swagger-jsdoc/commit/cf31676f7b33404d97d44ae1b1823b38bcae27d8) Base path isn’t needed anymore, because we aren’t modifying the UI’s source. (@calmdev)
- [da8bbc1](https://github.com/Surnet/swagger-jsdoc/commit/da8bbc132fc976145395c0604211022433be6e60) This doesn’t work with latest swagger UI. Also, people should be encouraged to update and build the swagger UI from source instead of relying on swagger-express to modify the default discovery URL. (@calmdev)
- [3b86bff](https://github.com/Surnet/swagger-jsdoc/commit/3b86bff51d2fc75c7618e77d71f424c52298d528) Serves the swagger static assets from specified swaggerUI path. (@calmdev)
- [c182a47](https://github.com/Surnet/swagger-jsdoc/commit/c182a470c41daea2f35615f14e7f7642f999f970) Adds option to specify swagger’s web interface url. (@calmdev)
- [ce80517](https://github.com/Surnet/swagger-jsdoc/commit/ce80517ae397a60b996683f6bcc3bf22e98e289d) Adds option to specify swagger’s JSON url. (@calmdev)
- [196d119](https://github.com/Surnet/swagger-jsdoc/commit/196d119c30d53bb328e9c1177f26b4b2515580e1) Update README.md (@fliptoo)
- [37a06c1](https://github.com/Surnet/swagger-jsdoc/commit/37a06c1e89c1d16311d00a2371bfa1df2b74fd18) update version (@fliptoo)
- [69dc86d](https://github.com/Surnet/swagger-jsdoc/commit/69dc86d96875782a78bb8d27095387dce80d7b7a) commit package.json (@fliptoo)
- [#1](https://github.com/Surnet/swagger-jsdoc/pull/1) Merge pull request #1 from stelcheck/develop (@stelcheck)
- [579f1bb](https://github.com/Surnet/swagger-jsdoc/commit/579f1bb9bed6f82ea20757f67eed965e78f65fb5) \* Bugfix: was using path instead of resourcePath (@stelcheck)
- [98fe645](https://github.com/Surnet/swagger-jsdoc/commit/98fe6456ba7848eb212cf45b085911bce9493e0d) \* Code linting (@stelcheck)
- [53212be](https://github.com/Surnet/swagger-jsdoc/commit/53212bec50712856db318fedbcf9b7133a041a1b) Update README.md (@fliptoo)
- [7a6f4b2](https://github.com/Surnet/swagger-jsdoc/commit/7a6f4b2b2fa56c51f4bbf5d8f768a8b2bbe4e9df) Update README.md (@fliptoo)
- [d379519](https://github.com/Surnet/swagger-jsdoc/commit/d379519ee461922dc6b11a5b9aae92b64e281cda) fix require path (@fliptoo)
- [251fbaa](https://github.com/Surnet/swagger-jsdoc/commit/251fbaa623dd0f4c78744545f7d0e20958719507) fix resources (@fliptoo)
- [fed18da](https://github.com/Surnet/swagger-jsdoc/commit/fed18da6efaefdf05ae16fb8c8b1f64734dbf8e7) edit npmignore (@fliptoo)
- [086f3fc](https://github.com/Surnet/swagger-jsdoc/commit/086f3fc8ea69c51f90f9aaf5be48fe420db08d1e) change to example (@fliptoo)
- [4f27c86](https://github.com/Surnet/swagger-jsdoc/commit/4f27c8634b61eb7c4bb711fbe82a688cff976b50) Update README.md (@fliptoo)
- [6a2e19c](https://github.com/Surnet/swagger-jsdoc/commit/6a2e19c7bf1e671369c79d777e4832ed2ccc3f3c) Create README.md (@fliptoo)
- [7b1d808](https://github.com/Surnet/swagger-jsdoc/commit/7b1d80862389dd7aacede4a4bca1d3b423ea0aec) initial commit (@fliptoo)
- [6f64e49](https://github.com/Surnet/swagger-jsdoc/commit/6f64e4992d7953c69694ea87177b3cd59fd9da43) Initial commit (@fliptoo)
