const NodeCache = require('node-cache');

class CacheService {
  constructor() {
    // Initialize in-memory cache with default TTL of 10 minutes
    this.cache = new NodeCache({ 
      stdTTL: 600, // 10 minutes
      checkperiod: 120, // Check for expired keys every 2 minutes
      useClones: false // Better performance, but be careful with object mutations
    });

    // Cache statistics
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };

    console.log('✅ Cache service initialized');
  }

  // Get value from cache
  get(key) {
    const value = this.cache.get(key);
    if (value !== undefined) {
      this.stats.hits++;
      console.log(`Cache HIT: ${key}`);
      return value;
    } else {
      this.stats.misses++;
      console.log(`Cache MISS: ${key}`);
      return null;
    }
  }

  // Set value in cache
  set(key, value, ttl = null) {
    const success = this.cache.set(key, value, ttl);
    if (success) {
      this.stats.sets++;
      console.log(`Cache SET: ${key}${ttl ? ` (TTL: ${ttl}s)` : ''}`);
    }
    return success;
  }

  // Delete value from cache
  del(key) {
    const success = this.cache.del(key);
    if (success) {
      this.stats.deletes++;
      console.log(`Cache DELETE: ${key}`);
    }
    return success;
  }

  // Check if key exists
  has(key) {
    return this.cache.has(key);
  }

  // Get multiple keys
  mget(keys) {
    return this.cache.mget(keys);
  }

  // Set multiple key-value pairs
  mset(keyValuePairs) {
    return this.cache.mset(keyValuePairs);
  }

  // Clear all cache
  flush() {
    this.cache.flushAll();
    console.log('Cache flushed');
  }

  // Get cache statistics
  getStats() {
    const cacheStats = this.cache.getStats();
    return {
      ...this.stats,
      keys: cacheStats.keys,
      hits_ratio: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      memory_usage: process.memoryUsage()
    };
  }

  // Cache middleware for Express routes
  middleware(ttl = 600) {
    return (req, res, next) => {
      // Only cache GET requests
      if (req.method !== 'GET') {
        return next();
      }

      // Create cache key from URL and query parameters
      const key = this.createCacheKey(req);
      
      // Try to get from cache
      const cachedResponse = this.get(key);
      if (cachedResponse) {
        return res.json(cachedResponse);
      }

      // Store original res.json
      const originalJson = res.json;

      // Override res.json to cache the response
      res.json = (data) => {
        // Cache successful responses only
        if (res.statusCode === 200 && data.success !== false) {
          this.set(key, data, ttl);
        }
        
        // Call original res.json
        return originalJson.call(res, data);
      };

      next();
    };
  }

  // Create cache key from request
  createCacheKey(req) {
    const baseKey = req.originalUrl || req.url;
    const queryString = Object.keys(req.query).length > 0 
      ? JSON.stringify(req.query) 
      : '';
    const userKey = req.user ? req.user._id : 'anonymous';
    
    return `${baseKey}:${queryString}:${userKey}`;
  }

  // Cache with automatic refresh
  async getOrSet(key, fetchFunction, ttl = 600) {
    // Try to get from cache first
    let value = this.get(key);
    
    if (value !== null) {
      return value;
    }

    try {
      // Fetch new value
      value = await fetchFunction();
      
      // Cache the result
      this.set(key, value, ttl);
      
      return value;
    } catch (error) {
      console.error(`Error fetching data for cache key ${key}:`, error);
      throw error;
    }
  }

  // Invalidate cache by pattern
  invalidatePattern(pattern) {
    const keys = this.cache.keys();
    const regex = new RegExp(pattern);
    
    const keysToDelete = keys.filter(key => regex.test(key));
    
    keysToDelete.forEach(key => {
      this.del(key);
    });

    console.log(`Invalidated ${keysToDelete.length} cache keys matching pattern: ${pattern}`);
    return keysToDelete.length;
  }

  // Cache warming - preload frequently accessed data
  async warmCache() {
    console.log('Starting cache warming...');
    
    try {
      // Warm up services cache
      await this.warmServices();
      
      // Warm up popular data
      await this.warmPopularData();
      
      console.log('Cache warming completed');
    } catch (error) {
      console.error('Cache warming failed:', error);
    }
  }

  async warmServices() {
    try {
      const Service = require('../models/Service');
      const services = await Service.find({ isActive: true });
      
      // Cache all services
      this.set('services:all', services, 1800); // 30 minutes
      
      // Cache services by category
      const categories = [...new Set(services.map(s => s.category))];
      categories.forEach(category => {
        const categoryServices = services.filter(s => s.category === category);
        this.set(`services:category:${category}`, categoryServices, 1800);
      });
      
      console.log(`Warmed cache for ${services.length} services`);
    } catch (error) {
      console.error('Error warming services cache:', error);
    }
  }

  async warmPopularData() {
    try {
      // This would warm up frequently accessed data
      // Implementation depends on your specific use cases
      console.log('Popular data cache warmed');
    } catch (error) {
      console.error('Error warming popular data cache:', error);
    }
  }

  // Cache tags for organized invalidation
  setWithTags(key, value, tags = [], ttl = null) {
    // Set the main value
    this.set(key, value, ttl);
    
    // Store tag associations
    tags.forEach(tag => {
      const tagKey = `tag:${tag}`;
      let taggedKeys = this.get(tagKey) || [];
      
      if (!taggedKeys.includes(key)) {
        taggedKeys.push(key);
        this.set(tagKey, taggedKeys, ttl);
      }
    });
  }

  // Invalidate by tag
  invalidateByTag(tag) {
    const tagKey = `tag:${tag}`;
    const taggedKeys = this.get(tagKey) || [];
    
    taggedKeys.forEach(key => {
      this.del(key);
    });
    
    // Remove the tag itself
    this.del(tagKey);
    
    console.log(`Invalidated ${taggedKeys.length} cache keys with tag: ${tag}`);
    return taggedKeys.length;
  }

  // Health check
  healthCheck() {
    const stats = this.getStats();
    const isHealthy = stats.keys < 10000; // Arbitrary threshold
    
    return {
      status: isHealthy ? 'healthy' : 'warning',
      stats,
      message: isHealthy ? 'Cache is operating normally' : 'Cache has high memory usage'
    };
  }
}

// Export singleton instance
module.exports = new CacheService();
