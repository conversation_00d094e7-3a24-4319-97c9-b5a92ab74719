const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const serviceRoutes = require('./routes/services');
const bookingRoutes = require('./routes/bookings');
const reviewRoutes = require('./routes/reviews');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

const app = express();

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: (process.env.RATE_LIMIT_WINDOW || 15) * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_MAX_REQUESTS || 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://yourdomain.com']
    : ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],
  credentials: true
}));

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static('uploads'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  const dbConnected = global.dbConnected || false;
  res.status(dbConnected ? 200 : 503).json({
    success: dbConnected,
    message: dbConnected ? 'Backend server is running with database' : 'Backend server running but database connection required',
    timestamp: new Date().toISOString(),
    dbConnected,
    status: dbConnected ? 'healthy' : 'degraded'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/services', serviceRoutes);
app.use('/api/bookings', bookingRoutes);
app.use('/api/reviews', reviewRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Repair & Maintenance Services API',
    version: '1.0.0',
    documentation: '/api/docs'
  });
});



// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Import data service for database management
const { setDbConnection } = require('./services/dataService');

// Database connection - MongoDB Atlas
const connectDB = async () => {
  try {
    console.log('🔄 Attempting to connect to MongoDB Atlas...');
    console.log('Connection URI:', process.env.MONGODB_URI ? 'Set' : 'Not set');

    // Optimized connection options for MongoDB Atlas
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 30000, // Increased timeout for Atlas
      socketTimeoutMS: 75000, // Increased socket timeout for Atlas
      maxPoolSize: 10, // Maintain up to 10 socket connections
      heartbeatFrequencyMS: 10000, // Send heartbeat every 10s
      bufferCommands: false // Disable mongoose buffering
    });

    console.log(`✅ MongoDB Atlas Connected: ${conn.connection.host}`);
    console.log(`📊 Database: ${conn.connection.name}`);
    console.log(`🔗 Ready State: ${conn.connection.readyState}`);
    setDbConnection(true);

    // Handle connection events
    mongoose.connection.on('disconnected', () => {
      console.log('❌ MongoDB Atlas disconnected - Application functionality limited');
      setDbConnection(false);
    });

    mongoose.connection.on('reconnected', () => {
      console.log('✅ MongoDB Atlas reconnected - Full functionality restored');
      setDbConnection(true);
    });

    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB Atlas connection error:', err.message);
      setDbConnection(false);
    });

  } catch (error) {
    console.error('❌ CRITICAL: MongoDB Atlas connection failed!');
    console.error('Error name:', error.name);
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);

    if (error.reason) {
      console.error('Reason:', error.reason);
    }

    console.log('');
    console.log('🚨 Please check your MongoDB Atlas configuration:');
    console.log('   1. Verify connection string in .env file');
    console.log('   2. Check network access (IP whitelist)');
    console.log('   3. Verify database user credentials');
    console.log('   4. Ensure cluster is running');
    console.log('');
    console.log('🔄 Retrying connection in 15 seconds...');
    setDbConnection(false);

    // Retry connection every 15 seconds for Atlas
    setTimeout(connectDB, 15000);
  }
};

// Connect to database
connectDB();

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV || 'development'} mode on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  server.close(() => {
    process.exit(1);
  });
});

module.exports = app;
