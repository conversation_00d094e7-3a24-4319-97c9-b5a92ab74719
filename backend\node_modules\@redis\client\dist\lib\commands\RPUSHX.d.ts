import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    /**
     * Constructs the RPUSHX command
     *
     * @param parser - The command parser
     * @param key - The list key to push to (only if it exists)
     * @param element - One or more elements to push
     * @see https://redis.io/commands/rpushx/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, element: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=RPUSHX.d.ts.map