const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const emailService = require('../services/emailService');
const Booking = require('../models/Booking');
const User = require('../models/User');

// @desc    Send test email
// @route   POST /api/notifications/test-email
// @access  Private (Admin only)
router.post('/test-email', protect, authorize('admin'), async (req, res) => {
  try {
    const { email, subject, message } = req.body;
    
    if (!email || !subject || !message) {
      return res.status(400).json({
        success: false,
        message: 'Email, subject, and message are required'
      });
    }

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Test Email</h2>
        <p>${message}</p>
        <p>Best regards,<br>Repair & Maintenance Services Team</p>
      </div>
    `;

    const result = await emailService.sendEmail(email, subject, html);
    
    res.json({
      success: result.success,
      message: result.success ? 'Test email sent successfully' : 'Failed to send test email',
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Send booking confirmation email
// @route   POST /api/notifications/booking-confirmation
// @access  Private
router.post('/booking-confirmation', protect, async (req, res) => {
  try {
    const { bookingId } = req.body;
    
    if (!bookingId) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID is required'
      });
    }

    const booking = await Booking.findById(bookingId)
      .populate('customer', 'firstName lastName email')
      .populate('service', 'name category');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if user is authorized to send notification for this booking
    if (booking.customer._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to send notification for this booking'
      });
    }

    const result = await emailService.sendBookingConfirmation(booking, booking.customer);
    
    res.json({
      success: result.success,
      message: result.success ? 'Booking confirmation email sent' : 'Failed to send email',
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Send booking status update email
// @route   POST /api/notifications/status-update
// @access  Private (Admin/Technician)
router.post('/status-update', protect, authorize('admin', 'technician'), async (req, res) => {
  try {
    const { bookingId, oldStatus } = req.body;
    
    if (!bookingId || !oldStatus) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID and old status are required'
      });
    }

    const booking = await Booking.findById(bookingId)
      .populate('customer', 'firstName lastName email')
      .populate('service', 'name category')
      .populate('technician', 'firstName lastName');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    const result = await emailService.sendBookingStatusUpdate(booking, booking.customer, oldStatus);
    
    res.json({
      success: result.success,
      message: result.success ? 'Status update email sent' : 'Failed to send email',
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Send payment confirmation email
// @route   POST /api/notifications/payment-confirmation
// @access  Private
router.post('/payment-confirmation', protect, async (req, res) => {
  try {
    const { bookingId, paymentAmount } = req.body;
    
    if (!bookingId || !paymentAmount) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID and payment amount are required'
      });
    }

    const booking = await Booking.findById(bookingId)
      .populate('customer', 'firstName lastName email')
      .populate('service', 'name category');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if user is authorized
    if (booking.customer._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized'
      });
    }

    const result = await emailService.sendPaymentConfirmation(booking, booking.customer, paymentAmount);
    
    res.json({
      success: result.success,
      message: result.success ? 'Payment confirmation email sent' : 'Failed to send email',
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Send technician assignment email
// @route   POST /api/notifications/technician-assignment
// @access  Private (Admin only)
router.post('/technician-assignment', protect, authorize('admin'), async (req, res) => {
  try {
    const { bookingId, technicianId } = req.body;
    
    if (!bookingId || !technicianId) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID and technician ID are required'
      });
    }

    const booking = await Booking.findById(bookingId)
      .populate('customer', 'firstName lastName email')
      .populate('service', 'name category')
      .populate('serviceAddress');

    const technician = await User.findById(technicianId);

    if (!booking || !technician) {
      return res.status(404).json({
        success: false,
        message: 'Booking or technician not found'
      });
    }

    const result = await emailService.sendTechnicianAssignment(booking, technician);
    
    res.json({
      success: result.success,
      message: result.success ? 'Technician assignment email sent' : 'Failed to send email',
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Send welcome email to new user
// @route   POST /api/notifications/welcome
// @access  Public (used during registration)
router.post('/welcome', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const result = await emailService.sendWelcomeEmail(user);
    
    res.json({
      success: result.success,
      message: result.success ? 'Welcome email sent' : 'Failed to send email',
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
