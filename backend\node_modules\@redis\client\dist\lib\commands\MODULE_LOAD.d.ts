import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the MODULE LOAD command
     *
     * @param parser - The command parser
     * @param path - Path to the module file
     * @param moduleArguments - Optional arguments to pass to the module
     * @see https://redis.io/commands/module-load/
     */
    readonly parseCommand: (this: void, parser: CommandParser, path: RedisArgument, moduleArguments?: Array<RedisArgument>) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=MODULE_LOAD.d.ts.map