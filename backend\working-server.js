console.log('🚀 Starting backend server...');

const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

console.log('✅ Middleware configured');

// Test route
app.get('/', (req, res) => {
  console.log('📍 Root endpoint accessed');
  res.json({ message: 'Backend server is running!' });
});

// Health check
app.get('/api/health', (req, res) => {
  console.log('🏥 Health check requested');
  res.json({ 
    success: true, 
    message: 'Backend server is healthy',
    timestamp: new Date().toISOString()
  });
});

// Login endpoint
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login request received');
  console.log('Request body:', req.body);
  
  const { email, password } = req.body;
  
  // Simple test credentials
  if (email === '<EMAIL>' && password === '123456') {
    console.log('✅ Login successful for:', email);
    res.json({
      success: true,
      message: 'Login successful',
      token: 'test-token-123',
      user: {
        id: '1',
        firstName: 'Test',
        lastName: 'User',
        email: email,
        role: 'customer'
      }
    });
  } else {
    console.log('❌ Invalid credentials for:', email);
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Register endpoint
app.post('/api/auth/register', (req, res) => {
  console.log('📝 Register request received');
  console.log('Request body:', req.body);
  
  const { firstName, lastName, email, password, phone, role } = req.body;
  
  // Simple registration (just return success)
  console.log('✅ Registration successful for:', email);
  res.status(201).json({
    success: true,
    message: 'Registration successful',
    token: 'test-token-456',
    user: {
      id: '2',
      firstName: firstName,
      lastName: lastName,
      email: email,
      phone: phone,
      role: role || 'customer'
    }
  });
});

// Mock services endpoint
app.get('/api/services', (req, res) => {
  console.log('📋 Services list requested');
  res.json({
    success: true,
    data: [
      {
        _id: '1',
        name: 'Pipe Leak Repair',
        description: 'Professional pipe leak detection and repair',
        category: 'plumbing',
        basePrice: 72000, // 120 USD converted to FCFA
        estimatedDuration: 2,
        difficulty: 'medium',
        isActive: true
      },
      {
        _id: '2',
        name: 'Electrical Outlet Installation',
        description: 'Safe electrical outlet installation',
        category: 'electrical',
        basePrice: 90000, // 150 USD converted to FCFA
        estimatedDuration: 1.5,
        difficulty: 'medium',
        isActive: true
      },
      {
        _id: '3',
        name: 'AC Unit Maintenance',
        description: 'Complete air conditioning unit maintenance',
        category: 'hvac',
        basePrice: 120000, // 200 USD converted to FCFA
        estimatedDuration: 3,
        difficulty: 'medium',
        isActive: true
      }
    ]
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('❌ Error:', err);
  res.status(500).json({
    success: false,
    message: 'Server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  console.log('❓ 404 - Route not found:', req.originalUrl);
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
const PORT = 3002;

app.listen(PORT, () => {
  console.log('');
  console.log('🎉 ================================');
  console.log('🚀 BACKEND SERVER IS RUNNING!');
  console.log('🌐 Port:', PORT);
  console.log('📍 URL: http://localhost:' + PORT);
  console.log('🏥 Health: http://localhost:' + PORT + '/api/health');
  console.log('');
  console.log('🔑 Test Login Credentials:');
  console.log('   📧 Email: <EMAIL>');
  console.log('   🔐 Password: 123456');
  console.log('🎉 ================================');
  console.log('');
});

// Handle server errors
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Rejection:', err);
});
