import { Command<PERSON>arser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the SETRANGE command
     *
     * @param parser - The command parser
     * @param key - The key to modify
     * @param offset - The offset at which to start writing
     * @param value - The value to write at the offset
     * @see https://redis.io/commands/setrange/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, offset: number, value: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SETRANGE.d.ts.map