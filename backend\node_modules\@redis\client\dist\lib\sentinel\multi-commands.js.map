{"version": 3, "file": "multi-commands.js", "sourceRoot": "", "sources": ["../../../lib/sentinel/multi-commands.ts"], "names": [], "mappings": ";;;;;AAAA,2DAAmC;AACnC,qEAA8F;AAE9F,4CAAwF;AAExF,6CAAsD;AAmFtD,MAAqB,yBAAyB;IACpC,MAAM,CAAC,cAAc,CAAC,OAAgB,EAAE,IAAkB;QAChE,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAExD,OAAO,UAA2C,GAAG,IAAoB;YACvE,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAEtC,MAAM,SAAS,GAAqB,MAAM,CAAC,SAAS,CAAC;YACrD,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAErC,OAAO,IAAI,CAAC,UAAU,CACpB,OAAO,CAAC,YAAY,EACpB,SAAS,EACT,cAAc,CACf,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,OAAgB,EAAE,IAAkB;QACtE,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAExD,OAAO,UAAsD,GAAG,IAAoB;YAClF,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAEtC,MAAM,SAAS,GAAqB,MAAM,CAAC,SAAS,CAAC;YACrD,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAErC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAC1B,OAAO,CAAC,YAAY,EACpB,SAAS,EACT,cAAc,CACf,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,IAAY,EAAE,EAAiB,EAAE,IAAkB;QACvF,MAAM,MAAM,GAAG,IAAA,mCAAuB,EAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAEnD,OAAO,UAAsD,GAAG,IAAoB;YAClF,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;YACvB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAEjC,MAAM,SAAS,GAAqB,MAAM,CAAC,SAAS,CAAC;YACrD,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAErC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAC1B,EAAE,CAAC,YAAY,EACf,SAAS,EACT,cAAc,CACf,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,MAAmB,EAAE,IAAkB;QACzE,MAAM,cAAc,GAAG,IAAA,6BAAiB,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEvD,OAAO,UAA2C,GAAG,IAAoB;YACvE,MAAM,MAAM,GAAG,IAAI,2BAAkB,EAAE,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAErC,MAAM,UAAU,GAAqB,MAAM,CAAC,SAAS,CAAC;YACtD,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAEtC,OAAO,IAAI,CAAC,UAAU,CACpB,MAAM,CAAC,YAAY,EACnB,MAAM,EACN,UAAU,EACV,cAAc,CACf,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,MAAM,CAKX,MAAuC;QACvC,OAAO,IAAA,wBAAY,EAAC;YAClB,SAAS,EAAE,yBAAyB;YACpC,QAAQ,EAAE,kBAAQ;YAClB,aAAa,EAAE,yBAAyB,CAAC,cAAc;YACvD,mBAAmB,EAAE,yBAAyB,CAAC,oBAAoB;YACnE,qBAAqB,EAAE,yBAAyB,CAAC,sBAAsB;YACvE,mBAAmB,EAAE,yBAAyB,CAAC,oBAAoB;YACnE,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAEQ,MAAM,GAAG,IAAI,uBAAiB,EAAE,CAAC;IACjC,SAAS,CAAmB;IACrC,WAAW,GAAwB,IAAI,CAAC;IAExC,YAAY,QAA2B,EAAE,WAAwB;QAC/D,IAAI,CAAC,MAAM,GAAG,IAAI,uBAAiB,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,SAAS,CACP,UAA+B;QAE/B,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC;IAClC,CAAC;IAED,UAAU,CACR,UAA+B,EAC/B,IAAsB,EACtB,cAA+B;QAE/B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU,CACR,UAA+B,EAC/B,MAAmB,EACnB,IAAsB,EACtB,cAA+B;QAE/B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;QAEpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,IAAI,CAAgD,cAAc,GAAG,KAAK;QAC9E,IAAI,cAAc;YAAE,OAAO,IAAI,CAAC,cAAc,EAAK,CAAC;QAEpD,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CACjC,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAChC,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAClB,CAC4B,CAAC;IAClC,CAAC;IAED,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAEjB,SAAS,CAAC,cAAc,GAAG,KAAK;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAuB,cAAc,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAgC,CAAC;QAE5E,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CACjC,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CACnC,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAClB,CAC4B,CAAC;IAClC,CAAC;IAED,mBAAmB;QACjB,OAAO,IAAI,CAAC,cAAc,EAAwB,CAAC;IACrD,CAAC;CACF;AAjKD,4CAiKC"}