import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns the number of elements in the sorted set with a score between min and max.
     * @param parser - The Redis command parser.
     * @param key - Key of the sorted set.
     * @param min - Minimum score to count from (inclusive).
     * @param max - Maximum score to count to (inclusive).
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, min: number | RedisArgument, max: number | RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ZCOUNT.d.ts.map