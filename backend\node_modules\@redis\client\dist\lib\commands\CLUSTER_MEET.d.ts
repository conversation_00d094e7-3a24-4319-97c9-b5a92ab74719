import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Initiates a handshake with another node in the cluster
     * @param parser - The Redis command parser
     * @param host - Host name or IP address of the node
     * @param port - TCP port of the node
     */
    readonly parseCommand: (this: void, parser: CommandParser, host: string, port: number) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=CLUSTER_MEET.d.ts.map