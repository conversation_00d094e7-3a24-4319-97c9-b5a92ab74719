"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ADD_1 = __importDefault(require("./ADD"));
const ADDNX_1 = __importDefault(require("./ADDNX"));
const COUNT_1 = __importDefault(require("./COUNT"));
const DEL_1 = __importDefault(require("./DEL"));
const EXISTS_1 = __importDefault(require("./EXISTS"));
const INFO_1 = __importDefault(require("./INFO"));
const INSERT_1 = __importDefault(require("./INSERT"));
const INSERTNX_1 = __importDefault(require("./INSERTNX"));
const LOADCHUNK_1 = __importDefault(require("./LOADCHUNK"));
const RESERVE_1 = __importDefault(require("./RESERVE"));
const SCANDUMP_1 = __importDefault(require("./SCANDUMP"));
exports.default = {
    ADD: ADD_1.default,
    add: ADD_1.default,
    ADDNX: ADDNX_1.default,
    addNX: ADDNX_1.default,
    COUNT: COUNT_1.default,
    count: COUNT_1.default,
    DEL: DEL_1.default,
    del: DEL_1.default,
    EXISTS: EXISTS_1.default,
    exists: EXISTS_1.default,
    INFO: INFO_1.default,
    info: INFO_1.default,
    INSERT: INSERT_1.default,
    insert: INSERT_1.default,
    INSERTNX: INSERTNX_1.default,
    insertNX: INSERTNX_1.default,
    LOADCHUNK: LOADCHUNK_1.default,
    loadChunk: LOADCHUNK_1.default,
    RESERVE: RESERVE_1.default,
    reserve: RESERVE_1.default,
    SCANDUMP: SCANDUMP_1.default,
    scanDump: SCANDUMP_1.default
};
//# sourceMappingURL=index.js.map