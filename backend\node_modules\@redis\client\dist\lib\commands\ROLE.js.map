{"version": 3, "file": "ROLE.js", "sourceRoot": "", "sources": ["../../../lib/commands/ROLE.ts"], "names": [], "mappings": ";;AAoCA,kBAAe;IACb,iBAAiB,EAAE,IAAI;IACvB,YAAY,EAAE,IAAI;IAClB;;;;;OAKG;IACH,YAAY,CAAC,MAAqB;QAChC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IACD;;;;;OAKG;IACH,cAAc,CAAC,KAAwB;QACrC,QAAQ,KAAK,CAAC,CAAC,CAA4C,EAAE,CAAC;YAC5D,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,CAAC,GAAG,KAAmB,CAAC;gBAChE,OAAO;oBACL,IAAI;oBACJ,iBAAiB;oBACjB,QAAQ,EAAG,QAAoD,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;wBAC5E,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,GAAG,OAAiD,CAAC;wBAC1F,OAAO;4BACL,IAAI;4BACJ,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;4BAClB,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,CAAC;yBAC7C,CAAC;oBACJ,CAAC,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,KAAK,OAAO,CAAC,CAAC,CAAC;gBACb,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,CAAC,GAAG,KAAkB,CAAC;gBAC/E,OAAO;oBACL,IAAI;oBACJ,MAAM,EAAE;wBACN,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,UAAU;qBACjB;oBACD,KAAK;oBACL,YAAY;iBACb,CAAC;YACJ,CAAC;YAED,KAAK,UAAU,CAAC,CAAC,CAAC;gBAChB,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,KAAqB,CAAC;gBAClD,OAAO;oBACL,IAAI;oBACJ,WAAW;iBACZ,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACyB,CAAC"}