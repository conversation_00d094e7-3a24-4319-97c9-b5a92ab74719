import api from './api';

export const authService = {
  // Login user
  login: async (email, password) => {
    const response = await api.post('/auth/login', { email, password });
    return response;
  },

  // Register user
  register: async (userData) => {
    console.log('AuthService: Sending registration request with data:', userData);
    try {
      const response = await api.post('/auth/register', userData);
      console.log('AuthService: Registration successful:', response);
      return response;
    } catch (error) {
      console.error('AuthService: Registration failed:', error);
      throw error;
    }
  },

  // Get current user
  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response;
  },

  // Logout user
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response;
  }
};
