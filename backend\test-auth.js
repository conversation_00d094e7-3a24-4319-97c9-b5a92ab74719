const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('./models/User');

const testAuth = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create a test user with known credentials
    const testEmail = '<EMAIL>';
    const testPassword = '123456';

    // Delete existing test user
    await User.deleteOne({ email: testEmail });

    // Hash password manually
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    console.log('🔐 Hashed password:', hashedPassword);

    // Create user directly
    const user = new User({
      firstName: 'Test',
      lastName: 'User',
      email: testEmail,
      password: hashedPassword,
      phone: '1234567890',
      role: 'customer',
      isActive: true
    });

    await user.save();
    console.log('✅ Test user created');

    // Now test login by finding user and comparing password
    const foundUser = await User.findOne({ email: testEmail }).select('+password');
    console.log('👤 Found user:', foundUser ? 'YES' : 'NO');
    
    if (foundUser) {
      console.log('📧 Email:', foundUser.email);
      console.log('🔑 Stored password hash:', foundUser.password);
      console.log('🔒 Active:', foundUser.isActive);
      
      // Test password comparison
      const isMatch = await bcrypt.compare(testPassword, foundUser.password);
      console.log('🔐 Password match:', isMatch ? '✅ SUCCESS' : '❌ FAILED');
      
      // Test using the model method
      const isMatchMethod = await foundUser.comparePassword(testPassword);
      console.log('🔐 Model method match:', isMatchMethod ? '✅ SUCCESS' : '❌ FAILED');
    }

    console.log('\n🎯 TEST CREDENTIALS:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: 123456');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
};

testAuth();
