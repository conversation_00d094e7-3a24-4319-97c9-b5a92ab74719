import { CommandParser } from '../client/parser';
import { NumberReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns the number of keys in the specified hash slot
     * @param parser - The Redis command parser
     * @param slot - The hash slot to check
     */
    readonly parseCommand: (this: void, parser: CommandParser, slot: number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=CLUSTER_COUNTKEYSINSLOT.d.ts.map