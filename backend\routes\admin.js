const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const User = require('../models/User');
const Booking = require('../models/Booking');
const Service = require('../models/Service');
const Review = require('../models/Review');
const socketService = require('../services/socketService');

// @desc    Get admin dashboard analytics
// @route   GET /api/admin/analytics
// @access  Private (Admin only)
router.get('/analytics', protect, authorize('admin'), async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // User statistics
    const totalUsers = await User.countDocuments();
    const newUsers = await User.countDocuments({ createdAt: { $gte: startDate } });
    const usersByRole = await User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    // Booking statistics
    const totalBookings = await Booking.countDocuments();
    const newBookings = await Booking.countDocuments({ createdAt: { $gte: startDate } });
    const bookingsByStatus = await Booking.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Revenue statistics
    const revenueData = await Booking.aggregate([
      { $match: { status: 'completed', createdAt: { $gte: startDate } } },
      { $group: { _id: null, total: { $sum: '$pricing.totalAmount' } } }
    ]);

    const monthlyRevenue = await Booking.aggregate([
      { $match: { status: 'completed' } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          revenue: { $sum: '$pricing.totalAmount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ]);

    // Service statistics
    const popularServices = await Booking.aggregate([
      { $group: { _id: '$service', count: { $sum: 1 } } },
      { $lookup: { from: 'services', localField: '_id', foreignField: '_id', as: 'serviceInfo' } },
      { $unwind: '$serviceInfo' },
      { $project: { name: '$serviceInfo.name', category: '$serviceInfo.category', count: 1 } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Review statistics
    const averageRating = await Review.aggregate([
      { $group: { _id: null, avgRating: { $avg: '$rating' } } }
    ]);

    // System statistics
    const connectedUsers = socketService.getConnectedUsersCount();

    res.json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          new: newUsers,
          byRole: usersByRole,
          connected: connectedUsers
        },
        bookings: {
          total: totalBookings,
          new: newBookings,
          byStatus: bookingsByStatus
        },
        revenue: {
          total: revenueData[0]?.total || 0,
          monthly: monthlyRevenue
        },
        services: {
          popular: popularServices
        },
        reviews: {
          averageRating: averageRating[0]?.avgRating || 0
        },
        period: parseInt(period)
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Get all users with pagination and filters
// @route   GET /api/admin/users
// @access  Private (Admin only)
router.get('/users', protect, authorize('admin'), async (req, res) => {
  try {
    const { page = 1, limit = 10, role, search, status } = req.query;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = {};
    if (role) filter.role = role;
    if (status) filter.isActive = status === 'active';
    if (search) {
      filter.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const users = await User.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(filter);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Update user status
// @route   PUT /api/admin/users/:id/status
// @access  Private (Admin only)
router.put('/users/:id/status', protect, authorize('admin'), async (req, res) => {
  try {
    const { isActive } = req.body;
    
    const user = await User.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Get all bookings with advanced filters
// @route   GET /api/admin/bookings
// @access  Private (Admin only)
router.get('/bookings', protect, authorize('admin'), async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status, 
      service, 
      technician, 
      dateFrom, 
      dateTo,
      search 
    } = req.query;
    
    const skip = (page - 1) * limit;

    // Build filter
    const filter = {};
    if (status) filter.status = status;
    if (service) filter.service = service;
    if (technician) filter.technician = technician;
    if (dateFrom || dateTo) {
      filter.scheduledDate = {};
      if (dateFrom) filter.scheduledDate.$gte = new Date(dateFrom);
      if (dateTo) filter.scheduledDate.$lte = new Date(dateTo);
    }

    const bookings = await Booking.find(filter)
      .populate('customer', 'firstName lastName email')
      .populate('service', 'name category')
      .populate('technician', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Booking.countDocuments(filter);

    res.json({
      success: true,
      data: {
        bookings,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Assign technician to booking
// @route   PUT /api/admin/bookings/:id/assign
// @access  Private (Admin only)
router.put('/bookings/:id/assign', protect, authorize('admin'), async (req, res) => {
  try {
    const { technicianId } = req.body;
    
    const booking = await Booking.findByIdAndUpdate(
      req.params.id,
      { 
        technician: technicianId,
        status: 'assigned'
      },
      { new: true }
    ).populate('customer service technician');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Send notification to technician
    socketService.emitBookingStatusUpdate(
      booking._id,
      'assigned',
      booking.customer._id,
      technicianId
    );

    res.json({
      success: true,
      data: booking
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Get system health status
// @route   GET /api/admin/system-health
// @access  Private (Admin only)
router.get('/system-health', protect, authorize('admin'), async (req, res) => {
  try {
    const health = {
      database: 'connected',
      websocket: socketService.getConnectedUsersCount() > 0 ? 'active' : 'inactive',
      connectedUsers: socketService.getConnectedUsersCount(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date()
    };

    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
