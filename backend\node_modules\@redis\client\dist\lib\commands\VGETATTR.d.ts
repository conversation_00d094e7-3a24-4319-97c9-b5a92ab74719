import { CommandParser } from '../client/parser';
import { RedisArgument } from '../RESP/types';
import { transformRedisJsonNullReply } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Retrieve the attributes of a vector set element
     *
     * @param parser - The command parser
     * @param key - The key of the vector set
     * @param element - The name of the element to retrieve attributes for
     * @see https://redis.io/commands/vgetattr/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, element: RedisArgument) => void;
    readonly transformReply: typeof transformRedisJsonNullReply;
};
export default _default;
//# sourceMappingURL=VGETATTR.d.ts.map