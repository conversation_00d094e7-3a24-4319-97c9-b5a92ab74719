import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply } from '../RESP/types';
declare const _default: {
    /**
     * Increments the float value of a field in a hash by the given amount
     * @param parser - The Redis command parser
     * @param key - Key of the hash
     * @param field - Field to increment
     * @param increment - Increment amount (float)
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, field: RedisArgument, increment: number) => void;
    readonly transformReply: () => BlobStringReply;
};
export default _default;
//# sourceMappingURL=HINCRBYFLOAT.d.ts.map