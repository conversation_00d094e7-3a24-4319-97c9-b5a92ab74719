import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns the replica nodes replicating from the specified primary node
     * @param parser - The Redis command parser
     * @param nodeId - Node ID of the primary node
     */
    readonly parseCommand: (this: void, parser: CommandParser, nodeId: RedisArgument) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=CLUSTER_REPLICAS.d.ts.map