/**
 * Currency formatting utilities for FCFA (Central African CFA franc)
 */

/**
 * Format amount to FCFA currency
 * @param {number} amount - The amount to format
 * @param {object} options - Formatting options
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, options = {}) => {
  const {
    showSymbol = true,
    showDecimals = false,
    locale = 'fr-CF' // Central African Republic locale
  } = options;

  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? '0 FCFA' : '0';
  }

  // Convert to number if it's a string
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Format with French locale for proper number formatting
  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0,
    useGrouping: true
  });

  const formattedNumber = formatter.format(numAmount);

  return showSymbol ? `${formattedNumber} FCFA` : formattedNumber;
};

/**
 * Format amount to FCFA with symbol
 * @param {number} amount - The amount to format
 * @returns {string} Formatted currency string with FCFA symbol
 */
export const formatFCFA = (amount) => {
  return formatCurrency(amount, { showSymbol: true, showDecimals: false });
};

/**
 * Format amount to FCFA with decimals
 * @param {number} amount - The amount to format
 * @returns {string} Formatted currency string with decimals
 */
export const formatFCFAWithDecimals = (amount) => {
  return formatCurrency(amount, { showSymbol: true, showDecimals: true });
};

/**
 * Parse FCFA string to number
 * @param {string} fcfaString - FCFA formatted string
 * @returns {number} Parsed number
 */
export const parseFCFA = (fcfaString) => {
  if (!fcfaString || typeof fcfaString !== 'string') {
    return 0;
  }

  // Remove FCFA symbol and spaces, then parse
  const cleanString = fcfaString
    .replace(/FCFA/g, '')
    .replace(/\s/g, '')
    .replace(/\u00A0/g, '') // Remove non-breaking spaces
    .replace(/,/g, ''); // Remove thousands separators

  return parseFloat(cleanString) || 0;
};

/**
 * Convert USD to FCFA (approximate conversion rate)
 * Note: In a real application, you would fetch current exchange rates
 * @param {number} usdAmount - Amount in USD
 * @param {number} exchangeRate - USD to FCFA exchange rate (default: ~600)
 * @returns {number} Amount in FCFA
 */
export const convertUSDToFCFA = (usdAmount, exchangeRate = 600) => {
  return usdAmount * exchangeRate;
};

/**
 * Format price range in FCFA
 * @param {number} minPrice - Minimum price
 * @param {number} maxPrice - Maximum price
 * @returns {string} Formatted price range
 */
export const formatPriceRange = (minPrice, maxPrice) => {
  if (minPrice === maxPrice) {
    return formatFCFA(minPrice);
  }
  return `${formatFCFA(minPrice)} - ${formatFCFA(maxPrice)}`;
};

// Export default formatCurrency function
export default formatCurrency;
