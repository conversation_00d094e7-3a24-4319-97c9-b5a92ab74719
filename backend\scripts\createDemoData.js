const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('../models/User');
const Service = require('../models/Service');
const Booking = require('../models/Booking');

const createDemoData = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await User.deleteMany({});
    await Service.deleteMany({});
    await Booking.deleteMany({});

    // Create demo users
    console.log('👥 Creating demo users...');
    const hashedPassword = await bcrypt.hash('password123', 12);

    const users = await User.create([
      {
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+1234567890',
        role: 'admin',
        isActive: true
      },
      {
        firstName: 'John',
        lastName: 'Customer',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+1234567891',
        role: 'customer',
        isActive: true,
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001'
        }
      },
      {
        firstName: 'Mike',
        lastName: 'Technician',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+1234567892',
        role: 'technician',
        isActive: true,
        specializations: ['plumbing', 'electrical'],
        rating: {
          average: 4.8,
          count: 25
        }
      },
      {
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '+1234567893',
        role: 'technician',
        isActive: true,
        specializations: ['hvac', 'appliance'],
        rating: {
          average: 4.6,
          count: 18
        }
      }
    ]);

    console.log(`✅ Created ${users.length} demo users`);

    // Create demo services
    console.log('🛠️ Creating demo services...');
    const services = await Service.create([
      {
        name: 'Pipe Leak Repair',
        description: 'Professional pipe leak detection and repair service. We fix all types of pipe leaks including kitchen, bathroom, and basement pipes.',
        category: 'plumbing',
        basePrice: 120,
        estimatedDuration: 2,
        difficulty: 'medium',
        isActive: true,
        requirements: ['Basic plumbing tools', 'Pipe sealant', 'Replacement parts if needed']
      },
      {
        name: 'Electrical Outlet Installation',
        description: 'Safe and professional electrical outlet installation. Perfect for adding new outlets or replacing old ones.',
        category: 'electrical',
        basePrice: 150,
        estimatedDuration: 1.5,
        difficulty: 'medium',
        isActive: true,
        requirements: ['Electrical tools', 'Wire nuts', 'Outlet box', 'Safety equipment']
      },
      {
        name: 'AC Unit Maintenance',
        description: 'Complete air conditioning unit maintenance including cleaning, filter replacement, and performance check.',
        category: 'hvac',
        basePrice: 200,
        estimatedDuration: 3,
        difficulty: 'medium',
        isActive: true,
        requirements: ['HVAC tools', 'Replacement filters', 'Cleaning supplies']
      },
      {
        name: 'Dishwasher Repair',
        description: 'Comprehensive dishwasher repair service for all major brands. Includes diagnosis and repair.',
        category: 'appliance',
        basePrice: 180,
        estimatedDuration: 2.5,
        difficulty: 'medium',
        isActive: true,
        requirements: ['Appliance repair tools', 'Diagnostic equipment', 'Common replacement parts']
      },
      {
        name: 'Ceiling Fan Installation',
        description: 'Professional ceiling fan installation with proper wiring and mounting for safety.',
        category: 'electrical',
        basePrice: 175,
        estimatedDuration: 2,
        difficulty: 'medium',
        isActive: true,
        requirements: ['Electrical tools', 'Mounting hardware', 'Wire nuts', 'Ladder']
      }
    ]);

    console.log(`✅ Created ${services.length} demo services`);

    // Create demo bookings
    console.log('📅 Creating demo bookings...');
    const customer = users.find(u => u.role === 'customer');
    const technician = users.find(u => u.role === 'technician');

    const bookings = await Booking.create([
      {
        service: services[0]._id,
        customer: customer._id,
        technician: technician._id,
        status: 'assigned',
        priority: 'medium',
        scheduledDate: new Date(Date.now() + 86400000), // Tomorrow
        scheduledTime: {
          start: '09:00',
          end: '11:00'
        },
        serviceAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001'
        },
        problemDescription: 'Kitchen sink pipe is leaking under the cabinet. Water damage is minimal but needs immediate attention.',
        pricing: {
          basePrice: services[0].basePrice,
          additionalCharges: [],
          discount: 0,
          tax: services[0].basePrice * 0.08,
          totalAmount: services[0].basePrice * 1.08
        }
      },
      {
        service: services[1]._id,
        customer: customer._id,
        status: 'confirmed',
        priority: 'low',
        scheduledDate: new Date(Date.now() + *********), // Day after tomorrow
        scheduledTime: {
          start: '14:00',
          end: '15:30'
        },
        serviceAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001'
        },
        problemDescription: 'Need to install two new electrical outlets in the home office.',
        pricing: {
          basePrice: services[1].basePrice,
          additionalCharges: [
            { description: 'Additional outlet', amount: 75 }
          ],
          discount: 0,
          tax: (services[1].basePrice + 75) * 0.08,
          totalAmount: (services[1].basePrice + 75) * 1.08
        }
      },
      {
        service: services[2]._id,
        customer: customer._id,
        status: 'pending',
        priority: 'high',
        scheduledDate: new Date(Date.now() + *********), // 3 days from now
        serviceAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001'
        },
        problemDescription: 'AC unit not cooling properly. Needs maintenance and filter replacement.',
        pricing: {
          basePrice: services[2].basePrice,
          additionalCharges: [],
          discount: 20, // $20 discount
          tax: (services[2].basePrice - 20) * 0.08,
          totalAmount: (services[2].basePrice - 20) * 1.08
        }
      }
    ]);

    console.log(`✅ Created ${bookings.length} demo bookings`);

    console.log('\n🎉 Demo data created successfully!');
    console.log('\n📋 Demo Accounts:');
    console.log('👤 Admin: <EMAIL> / password123');
    console.log('👤 Customer: <EMAIL> / password123');
    console.log('👤 Technician: <EMAIL> / password123');
    console.log('\n🚀 You can now test the application with these accounts!');

  } catch (error) {
    console.error('❌ Error creating demo data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
};

// Run the script
createDemoData();
