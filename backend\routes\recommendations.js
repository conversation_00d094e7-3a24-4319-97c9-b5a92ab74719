const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const recommendationService = require('../services/recommendationService');

// @desc    Get personalized service recommendations
// @route   GET /api/recommendations/personalized
// @access  Private
router.get('/personalized', protect, async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    
    const recommendations = await recommendationService.getPersonalizedRecommendations(
      req.user._id,
      parseInt(limit)
    );
    
    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Get collaborative filtering recommendations
// @route   GET /api/recommendations/collaborative
// @access  Private
router.get('/collaborative', protect, async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    
    const recommendations = await recommendationService.getCollaborativeRecommendations(
      req.user._id,
      parseInt(limit)
    );
    
    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Get trending services
// @route   GET /api/recommendations/trending
// @access  Public
router.get('/trending', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const trending = await recommendationService.getTrendingServices();
    
    res.json({
      success: true,
      data: trending.slice(0, parseInt(limit))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// @desc    Get seasonal recommendations
// @route   GET /api/recommendations/seasonal
// @access  Public
router.get('/seasonal', async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    
    const seasonal = await recommendationService.getSeasonalRecommendations();
    
    res.json({
      success: true,
      data: seasonal.slice(0, parseInt(limit))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
