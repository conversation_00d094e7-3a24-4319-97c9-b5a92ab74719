import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the KEYS command
     *
     * @param parser - The command parser
     * @param pattern - The pattern to match keys against
     * @see https://redis.io/commands/keys/
     */
    readonly parseCommand: (this: void, parser: CommandParser, pattern: RedisArgument) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=KEYS.d.ts.map