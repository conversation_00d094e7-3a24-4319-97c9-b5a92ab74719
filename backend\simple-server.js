const express = require('express');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

console.log('Starting simple server...');

app.post('/api/auth/login', (req, res) => {
  console.log('=== LOGIN REQUEST ===');
  console.log('Body:', req.body);
  
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === '123456') {
    console.log('✅ Login successful');
    res.json({
      success: true,
      message: 'Login successful',
      user: {
        firstName: 'Test',
        lastName: 'User',
        role: 'customer'
      }
    });
  } else {
    console.log('❌ Invalid credentials');
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({ success: true, message: 'Server is running' });
});

const PORT = 3002;
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log('📧 Test email: <EMAIL>');
  console.log('🔑 Test password: 123456');
});
