"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Constructs the SCRIPT DEBUG command
     *
     * @param parser - The command parser
     * @param mode - Debug mode: YES, SYNC, or NO
     * @see https://redis.io/commands/script-debug/
     */
    parseCommand(parser, mode) {
        parser.push('SCRIPT', 'DEBUG', mode);
    },
    transformReply: undefined
};
//# sourceMappingURL=SCRIPT_DEBUG.js.map