{"version": 3, "file": "AGGREGATE.js", "sourceRoot": "", "sources": ["../../../lib/commands/AGGREGATE.ts"], "names": [], "mappings": ";;;AAGA,qCAA+D;AAC/D,+FAA4F;AAC5F,gDAAqD;AAOxC,QAAA,kBAAkB,GAAG;IAChC,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,QAAQ;CACR,CAAC;AAUE,QAAA,8BAA8B,GAAG;IAC5C,KAAK,EAAE,OAAO;IACd,cAAc,EAAE,gBAAgB;IAChC,iBAAiB,EAAE,mBAAmB;IACtC,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,MAAM,EAAE,QAAQ;IAChB,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;IAChB,WAAW,EAAE,aAAa;IAC1B,aAAa,EAAE,eAAe;CACtB,CAAC;AAiGV,CAAC;AAEF,kBAAe;IACb,iBAAiB,EAAE,IAAI;IACvB,YAAY,EAAE,KAAK;IACnB;;;;;;;;;;;OAWG;IACH,YAAY,CAAC,MAAqB,EAAE,KAAoB,EAAE,KAAoB,EAAE,OAA4B;QAC1G,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAE1C,OAAO,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IACD,cAAc,EAAE;QACd,CAAC,EAAE,CAAC,QAA2B,EAAE,QAAc,EAAE,WAAyB,EAAkB,EAAE;YAC5F,MAAM,OAAO,GAAsD,EAAE,CAAC;YACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACzC,OAAO,CAAC,IAAI,CACV,IAAA,2CAAoB,EAAC,QAAQ,CAAC,CAAC,CAAgC,EAAE,QAAQ,EAAE,WAAW,CAAC,CACxF,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,8DAA8D;gBAC9D,kHAAkH;gBAClH,oEAAoE;gBACpE,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC1B,OAAO;aACR,CAAC;QACJ,CAAC;QACD,CAAC,EAAE,SAAwC;KAC5C;IACD,aAAa,EAAE,IAAI;CACO,CAAC;AAE7B,SAAgB,qBAAqB,CAAC,MAAqB,EAAG,OAA4B;IACxF,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;IAED,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC;IAED,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,GAAyB,EAAE,CAAC;QAEtC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBAChC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,OAAO,EAAE,OAAO,KAAK,SAAS,EAAE,CAAC;QACnC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;QACnB,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,0BAAkB,CAAC,OAAO;oBAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;wBACrB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACnB,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACjD,CAAC;oBAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC/B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;4BAClC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;wBACvC,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC3C,CAAC;oBAED,MAAM;gBAER,KAAK,0BAAkB,CAAC,MAAM;oBAC5B,MAAM,IAAI,GAAyB,EAAE,CAAC;oBAEtC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC3B,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;4BACzB,kBAAkB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBAC/B,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBACpC,CAAC;oBAED,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;wBACb,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACxC,CAAC;oBAED,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;oBAEpC,MAAM;gBAER,KAAK,0BAAkB,CAAC,KAAK;oBAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC5C,MAAM;gBAER,KAAK,0BAAkB,CAAC,KAAK;oBAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACxD,MAAM;gBAER,KAAK,0BAAkB,CAAC,MAAM;oBAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC7B,MAAM;YACV,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAA,4BAAmB,EAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAE7C,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;QACrB,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrD,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAe,CAAC,CAAC;IAC1C,CAAC;AACH,CAAC;AA1FD,sDA0FC;AAED,SAAS,aAAa,CAAC,IAA0B,EAAE,MAAiB;IAClE,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,YAAY,MAAM,EAAE,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpB,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAE7B,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAqB,EAAE,OAAwB;IAC1E,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAEpC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,sCAA8B,CAAC,KAAK;YACvC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,MAAM;QAER,KAAK,sCAA8B,CAAC,cAAc,CAAC;QACnD,KAAK,sCAA8B,CAAC,iBAAiB,CAAC;QACtD,KAAK,sCAA8B,CAAC,GAAG,CAAC;QACxC,KAAK,sCAA8B,CAAC,GAAG,CAAC;QACxC,KAAK,sCAA8B,CAAC,GAAG,CAAC;QACxC,KAAK,sCAA8B,CAAC,GAAG,CAAC;QACxC,KAAK,sCAA8B,CAAC,MAAM,CAAC;QAC3C,KAAK,sCAA8B,CAAC,MAAM;YACxC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM;QAER,KAAK,sCAA8B,CAAC,QAAQ;YAC1C,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YAChE,MAAM;QAER,KAAK,sCAA8B,CAAC,WAAW,CAAC,CAAC,CAAC;YAChD,MAAM,IAAI,GAAyB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAEtD,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC;gBACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChB,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,IAAI,OAAO,CAAC,EAAE,YAAY,MAAM,EAAE,CAAC;oBACnE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;oBAC/B,IAAI,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC;wBACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM;QACR,CAAC;QAED,KAAK,sCAA8B,CAAC,aAAa;YAC/C,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;YAClE,MAAM;IACV,CAAC;IAED,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC;QACf,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,IAA0B,EAAE,MAAsB;IAC5E,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,YAAY,MAAM,EAAE,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpB,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrB,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;AACH,CAAC"}