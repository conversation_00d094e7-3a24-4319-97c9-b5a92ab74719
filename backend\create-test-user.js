const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('./models/User');

const createTestUser = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Delete existing test user if exists
    await User.deleteOne({ email: '<EMAIL>' });

    // Create a simple test user
    console.log('👤 Creating test user...');
    const hashedPassword = await bcrypt.hash('123456', 12);
    
    const testUser = await User.create({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: hashedPassword,
      phone: '+1234567890',
      role: 'customer',
      isActive: true
    });

    console.log('✅ Test user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: 123456');

    // Verify the user can be found and password works
    const foundUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
    if (foundUser) {
      const isMatch = await bcrypt.compare('123456', foundUser.password);
      console.log(`🔐 Password verification: ${isMatch ? '✅ SUCCESS' : '❌ FAILED'}`);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
};

createTestUser();
