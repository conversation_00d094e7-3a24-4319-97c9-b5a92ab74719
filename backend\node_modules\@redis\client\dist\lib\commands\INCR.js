"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    /**
     * Constructs the INCR command
     *
     * @param parser - The command parser
     * @param key - The key to increment
     * @see https://redis.io/commands/incr/
     */
    parseCommand(parser, key) {
        parser.push('INCR');
        parser.pushKey(key);
    },
    transformReply: undefined
};
//# sourceMappingURL=INCR.js.map