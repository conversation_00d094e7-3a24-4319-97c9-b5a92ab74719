<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        .test-button {
            background: #2563eb;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1d4ed8;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .step h3 {
            margin-top: 0;
            color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Booking System Test</h1>
        
        <div class="step">
            <h3>Step 1: Test Backend Connection</h3>
            <p>First, let's check if the backend server is running and responding:</p>
            <button class="test-button" onclick="testBackend()">Test Backend Health</button>
            <div id="backend-result" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>Step 2: Test Services API</h3>
            <p>Check if services are being loaded correctly:</p>
            <button class="test-button" onclick="testServices()">Test Services API</button>
            <div id="services-result" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>Step 3: Test Service Detail</h3>
            <p>Test fetching a specific service for booking:</p>
            <button class="test-button" onclick="testServiceDetail()">Test Service Detail</button>
            <div id="service-detail-result" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>Step 4: Test Booking Creation</h3>
            <p>Test creating a new booking:</p>
            <button class="test-button" onclick="testBookingCreation()">Test Create Booking</button>
            <div id="booking-result" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>Step 5: Manual Booking Test</h3>
            <p>Try booking manually through the app:</p>
            <ol>
                <li>Make sure you're logged in with: <strong><EMAIL> / 123456</strong></li>
                <li>Go to <a href="http://localhost:5173/services" target="_blank">Services Page</a></li>
                <li>Click "Book Now" on any service</li>
                <li>Fill out the booking form</li>
                <li>Submit the booking</li>
            </ol>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3002/api';

        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing backend connection...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Backend is running!\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Backend error: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Connection failed: ${error.message}`;
            }
        }

        async function testServices() {
            const resultDiv = document.getElementById('services-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing services API...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch(`${API_BASE}/services`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Services loaded: ${data.data.length} services found\n${JSON.stringify(data.data.map(s => ({id: s._id, name: s.name, price: s.basePrice})), null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Services error: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Services request failed: ${error.message}`;
            }
        }

        async function testServiceDetail() {
            const resultDiv = document.getElementById('service-detail-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing service detail API...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch(`${API_BASE}/services/1`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Service detail loaded\n${JSON.stringify(data.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Service detail error: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Service detail request failed: ${error.message}`;
            }
        }

        async function testBookingCreation() {
            const resultDiv = document.getElementById('booking-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing booking creation...';
            resultDiv.className = 'result info';

            const bookingData = {
                service: '1',
                scheduledDate: new Date().toISOString().split('T')[0],
                scheduledTime: {
                    start: '09:00',
                    end: '11:00'
                },
                problemDescription: 'Test booking from booking test page',
                priority: 'medium',
                serviceAddress: {
                    street: '123 Test Street',
                    city: 'Test City',
                    state: 'Test State',
                    zipCode: '12345'
                },
                isEmergency: false
            };

            try {
                const response = await fetch(`${API_BASE}/bookings`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(bookingData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Booking created successfully!\nBooking Number: ${data.data.bookingNumber}\n${JSON.stringify(data.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Booking creation failed: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Booking request failed: ${error.message}`;
            }
        }
    </script>
</body>
</html>
