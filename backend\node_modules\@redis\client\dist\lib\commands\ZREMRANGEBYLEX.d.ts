import { CommandParser } from '../client/parser';
import { NumberReply, RedisArgument } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Removes all elements in the sorted set with lexicographical values between min and max.
     * @param parser - The Redis command parser.
     * @param key - Key of the sorted set.
     * @param min - Minimum lexicographical value.
     * @param max - Maximum lexicographical value.
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, min: RedisArgument | number, max: RedisArgument | number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ZREMRANGEBYLEX.d.ts.map