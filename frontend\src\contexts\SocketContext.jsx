import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { toast } from 'react-hot-toast';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const { user, token } = useAuth();

  useEffect(() => {
    if (user && token) {
      // Initialize socket connection
      const newSocket = io(import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:5001', {
        auth: {
          token: token
        }
      });

      newSocket.on('connect', () => {
        console.log('Connected to server');
        setIsConnected(true);
        setSocket(newSocket);
      });

      newSocket.on('disconnect', () => {
        console.log('Disconnected from server');
        setIsConnected(false);
      });

      // Listen for booking status updates
      newSocket.on('booking_status_updated', (data) => {
        console.log('Booking status updated:', data);
        toast.success(`Booking status updated to: ${data.status}`);
        
        // Add to notifications
        addNotification({
          id: Date.now(),
          type: 'booking_status',
          title: 'Booking Status Updated',
          message: `Your booking status has been updated to: ${data.status}`,
          data: data,
          timestamp: new Date()
        });
      });

      // Listen for new bookings (for technicians/admins)
      newSocket.on('new_booking', (data) => {
        console.log('New booking:', data);
        if (user.role === 'technician' || user.role === 'admin') {
          toast.success(`New booking: ${data.service}`);
          
          addNotification({
            id: Date.now(),
            type: 'new_booking',
            title: 'New Booking',
            message: `New booking for ${data.service} from ${data.customer}`,
            data: data,
            timestamp: new Date()
          });
        }
      });

      // Listen for payment confirmations
      newSocket.on('payment_confirmed', (data) => {
        console.log('Payment confirmed:', data);
        toast.success(`Payment of $${data.amount} confirmed!`);
        
        addNotification({
          id: Date.now(),
          type: 'payment',
          title: 'Payment Confirmed',
          message: `Payment of $${data.amount} has been confirmed`,
          data: data,
          timestamp: new Date()
        });
      });

      // Listen for chat messages
      newSocket.on('new_message', (data) => {
        console.log('New message:', data);
        if (data.senderId !== user.id) {
          toast.success(`New message from ${data.senderName}`);
          
          addNotification({
            id: Date.now(),
            type: 'message',
            title: 'New Message',
            message: `${data.senderName}: ${data.message}`,
            data: data,
            timestamp: new Date()
          });
        }
      });

      // Listen for technician location updates
      newSocket.on('technician_location_update', (data) => {
        console.log('Technician location updated:', data);
        // This could be used to update a map or show technician proximity
      });

      // Listen for typing indicators
      newSocket.on('user_typing_start', (data) => {
        console.log('User started typing:', data);
        // Handle typing indicator UI
      });

      newSocket.on('user_typing_stop', (data) => {
        console.log('User stopped typing:', data);
        // Handle typing indicator UI
      });

      // Listen for general notifications
      newSocket.on('notification', (data) => {
        console.log('Notification received:', data);
        toast.info(data.data.message || 'New notification');
        
        addNotification({
          id: Date.now(),
          type: data.type,
          title: data.data.title || 'Notification',
          message: data.data.message || 'You have a new notification',
          data: data.data,
          timestamp: new Date()
        });
      });

      return () => {
        newSocket.close();
        setSocket(null);
        setIsConnected(false);
      };
    }
  }, [user, token]);

  const addNotification = (notification) => {
    setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50 notifications
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Socket event emitters
  const emitBookingStatusUpdate = (bookingId, status, customerId, technicianId) => {
    if (socket) {
      socket.emit('booking_status_update', {
        bookingId,
        status,
        customerId,
        technicianId
      });
    }
  };

  const sendMessage = (bookingId, recipientId, message) => {
    if (socket) {
      socket.emit('send_message', {
        bookingId,
        recipientId,
        message
      });
    }
  };

  const updateLocation = (bookingId, customerId, latitude, longitude) => {
    if (socket) {
      socket.emit('location_update', {
        bookingId,
        customerId,
        latitude,
        longitude
      });
    }
  };

  const startTyping = (bookingId, recipientId) => {
    if (socket) {
      socket.emit('typing_start', {
        bookingId,
        recipientId
      });
    }
  };

  const stopTyping = (bookingId, recipientId) => {
    if (socket) {
      socket.emit('typing_stop', {
        bookingId,
        recipientId
      });
    }
  };

  const value = {
    socket,
    isConnected,
    notifications,
    onlineUsers,
    addNotification,
    removeNotification,
    clearAllNotifications,
    emitBookingStatusUpdate,
    sendMessage,
    updateLocation,
    startTyping,
    stopTyping
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
