{"name": "swagger-jsdoc", "description": "Generates swagger doc based on JSDoc", "version": "6.2.8", "engines": {"node": ">=12.0.0"}, "scripts": {"start": "node examples/app/app.js", "lint": "eslint .", "test:lint": "eslint .", "test:js": "jest --verbose", "test": "run-p test:* -cn"}, "main": "index.js", "bin": {"swagger-jsdoc": "./bin/swagger-jsdoc.js"}, "dependencies": {"commander": "6.2.0", "doctrine": "3.0.0", "glob": "7.1.6", "lodash.mergewith": "^4.6.2", "swagger-parser": "^10.0.3", "yaml": "2.0.0-1"}, "devDependencies": {"body-parser": "1.19.0", "eslint": "8.9.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "6.15.0", "eslint-loader": "4.0.2", "eslint-plugin-import": "2.25.4", "eslint-plugin-jest": "26.1.1", "eslint-plugin-prettier": "3.1.4", "express": "4.17.1", "husky": "7.0.4", "jest": "^26.6.1", "lint-staged": "10.5.2", "npm-run-all": "4.1.5", "prettier": "2.2.0", "supertest": "6.0.1"}, "license": "MIT", "homepage": "https://github.com/Surnet/swagger-jsdoc", "repository": {"type": "git", "url": "https://github.com/Surnet/swagger-jsdoc.git"}, "keywords": ["swagger", "openapi", "jsdoc"], "author": "https://github.com/Surnet/swagger-jsdoc/graphs/contributors", "bugs": {"url": "https://github.com/Surnet/swagger-jsdoc/issues"}, "resolutions": {"minimist": ">=1.2.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,md,yml,yaml}": ["prettier --write"]}}