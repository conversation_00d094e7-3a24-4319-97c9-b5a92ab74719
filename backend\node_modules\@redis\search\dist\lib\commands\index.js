"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const _LIST_1 = __importDefault(require("./_LIST"));
const ALTER_1 = __importDefault(require("./ALTER"));
const AGGREGATE_WITHCURSOR_1 = __importDefault(require("./AGGREGATE_WITHCURSOR"));
const AGGREGATE_1 = __importDefault(require("./AGGREGATE"));
const ALIASADD_1 = __importDefault(require("./ALIASADD"));
const ALIASDEL_1 = __importDefault(require("./ALIASDEL"));
const ALIASUPDATE_1 = __importDefault(require("./ALIASUPDATE"));
const CONFIG_GET_1 = __importDefault(require("./CONFIG_GET"));
const CONFIG_SET_1 = __importDefault(require("./CONFIG_SET"));
const CREATE_1 = __importDefault(require("./CREATE"));
const CURSOR_DEL_1 = __importDefault(require("./CURSOR_DEL"));
const CURSOR_READ_1 = __importDefault(require("./CURSOR_READ"));
const DICTADD_1 = __importDefault(require("./DICTADD"));
const DICTDEL_1 = __importDefault(require("./DICTDEL"));
const DICTDUMP_1 = __importDefault(require("./DICTDUMP"));
const DROPINDEX_1 = __importDefault(require("./DROPINDEX"));
const EXPLAIN_1 = __importDefault(require("./EXPLAIN"));
const EXPLAINCLI_1 = __importDefault(require("./EXPLAINCLI"));
const INFO_1 = __importDefault(require("./INFO"));
const PROFILE_SEARCH_1 = __importDefault(require("./PROFILE_SEARCH"));
const PROFILE_AGGREGATE_1 = __importDefault(require("./PROFILE_AGGREGATE"));
const SEARCH_NOCONTENT_1 = __importDefault(require("./SEARCH_NOCONTENT"));
const SEARCH_1 = __importDefault(require("./SEARCH"));
const SPELLCHECK_1 = __importDefault(require("./SPELLCHECK"));
const SUGADD_1 = __importDefault(require("./SUGADD"));
const SUGDEL_1 = __importDefault(require("./SUGDEL"));
const SUGGET_WITHPAYLOADS_1 = __importDefault(require("./SUGGET_WITHPAYLOADS"));
const SUGGET_WITHSCORES_WITHPAYLOADS_1 = __importDefault(require("./SUGGET_WITHSCORES_WITHPAYLOADS"));
const SUGGET_WITHSCORES_1 = __importDefault(require("./SUGGET_WITHSCORES"));
const SUGGET_1 = __importDefault(require("./SUGGET"));
const SUGLEN_1 = __importDefault(require("./SUGLEN"));
const SYNDUMP_1 = __importDefault(require("./SYNDUMP"));
const SYNUPDATE_1 = __importDefault(require("./SYNUPDATE"));
const TAGVALS_1 = __importDefault(require("./TAGVALS"));
exports.default = {
    _LIST: _LIST_1.default,
    _list: _LIST_1.default,
    ALTER: ALTER_1.default,
    alter: ALTER_1.default,
    AGGREGATE_WITHCURSOR: AGGREGATE_WITHCURSOR_1.default,
    aggregateWithCursor: AGGREGATE_WITHCURSOR_1.default,
    AGGREGATE: AGGREGATE_1.default,
    aggregate: AGGREGATE_1.default,
    ALIASADD: ALIASADD_1.default,
    aliasAdd: ALIASADD_1.default,
    ALIASDEL: ALIASDEL_1.default,
    aliasDel: ALIASDEL_1.default,
    ALIASUPDATE: ALIASUPDATE_1.default,
    aliasUpdate: ALIASUPDATE_1.default,
    /**
     * @deprecated Redis >=8 uses the standard CONFIG command
     */
    CONFIG_GET: CONFIG_GET_1.default,
    /**
     * @deprecated Redis >=8 uses the standard CONFIG command
     */
    configGet: CONFIG_GET_1.default,
    /**
     * @deprecated Redis >=8 uses the standard CONFIG command
     */
    CONFIG_SET: CONFIG_SET_1.default,
    /**
     * @deprecated Redis >=8 uses the standard CONFIG command
     */
    configSet: CONFIG_SET_1.default,
    CREATE: CREATE_1.default,
    create: CREATE_1.default,
    CURSOR_DEL: CURSOR_DEL_1.default,
    cursorDel: CURSOR_DEL_1.default,
    CURSOR_READ: CURSOR_READ_1.default,
    cursorRead: CURSOR_READ_1.default,
    DICTADD: DICTADD_1.default,
    dictAdd: DICTADD_1.default,
    DICTDEL: DICTDEL_1.default,
    dictDel: DICTDEL_1.default,
    DICTDUMP: DICTDUMP_1.default,
    dictDump: DICTDUMP_1.default,
    DROPINDEX: DROPINDEX_1.default,
    dropIndex: DROPINDEX_1.default,
    EXPLAIN: EXPLAIN_1.default,
    explain: EXPLAIN_1.default,
    EXPLAINCLI: EXPLAINCLI_1.default,
    explainCli: EXPLAINCLI_1.default,
    INFO: INFO_1.default,
    info: INFO_1.default,
    PROFILESEARCH: PROFILE_SEARCH_1.default,
    profileSearch: PROFILE_SEARCH_1.default,
    PROFILEAGGREGATE: PROFILE_AGGREGATE_1.default,
    profileAggregate: PROFILE_AGGREGATE_1.default,
    SEARCH_NOCONTENT: SEARCH_NOCONTENT_1.default,
    searchNoContent: SEARCH_NOCONTENT_1.default,
    SEARCH: SEARCH_1.default,
    search: SEARCH_1.default,
    SPELLCHECK: SPELLCHECK_1.default,
    spellCheck: SPELLCHECK_1.default,
    SUGADD: SUGADD_1.default,
    sugAdd: SUGADD_1.default,
    SUGDEL: SUGDEL_1.default,
    sugDel: SUGDEL_1.default,
    SUGGET_WITHPAYLOADS: SUGGET_WITHPAYLOADS_1.default,
    sugGetWithPayloads: SUGGET_WITHPAYLOADS_1.default,
    SUGGET_WITHSCORES_WITHPAYLOADS: SUGGET_WITHSCORES_WITHPAYLOADS_1.default,
    sugGetWithScoresWithPayloads: SUGGET_WITHSCORES_WITHPAYLOADS_1.default,
    SUGGET_WITHSCORES: SUGGET_WITHSCORES_1.default,
    sugGetWithScores: SUGGET_WITHSCORES_1.default,
    SUGGET: SUGGET_1.default,
    sugGet: SUGGET_1.default,
    SUGLEN: SUGLEN_1.default,
    sugLen: SUGLEN_1.default,
    SYNDUMP: SYNDUMP_1.default,
    synDump: SYNDUMP_1.default,
    SYNUPDATE: SYNUPDATE_1.default,
    synUpdate: SYNUPDATE_1.default,
    TAGVALS: TAGVALS_1.default,
    tagVals: TAGVALS_1.default
};
//# sourceMappingURL=index.js.map