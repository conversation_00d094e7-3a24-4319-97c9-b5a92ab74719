import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: false;
    /**
     * Deletes a library and all its functions
     * @param parser - The Redis command parser
     * @param library - Name of the library to delete
     */
    readonly parseCommand: (this: void, parser: CommandParser, library: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply;
};
export default _default;
//# sourceMappingURL=FUNCTION_DELETE.d.ts.map