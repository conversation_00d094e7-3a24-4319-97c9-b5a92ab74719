import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the INCRBY command
     *
     * @param parser - The command parser
     * @param key - The key to increment
     * @param increment - The amount to increment by
     * @see https://redis.io/commands/incrby/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, increment: number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=INCRBY.d.ts.map