"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    IS_READ_ONLY: true,
    /**
     * Retrieve the number of elements in a vector set
     *
     * @param parser - The command parser
     * @param key - The key of the vector set
     * @see https://redis.io/commands/vcard/
     */
    parseCommand(parser, key) {
        parser.push('VCARD');
        parser.pushKey(key);
    },
    transformReply: undefined
};
//# sourceMappingURL=VCARD.js.map