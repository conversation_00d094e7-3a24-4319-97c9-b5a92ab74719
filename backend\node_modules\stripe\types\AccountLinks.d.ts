// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    /**
     * Account Links are the means by which a Connect platform grants a connected account permission to access
     * Stripe-hosted applications, such as Connect Onboarding.
     *
     * Related guide: [Connect Onboarding](https://stripe.com/docs/connect/custom/hosted-onboarding)
     */
    interface AccountLink {
      /**
       * String representing the object's type. Objects of the same type share the same value.
       */
      object: 'account_link';

      /**
       * Time at which the object was created. Measured in seconds since the Unix epoch.
       */
      created: number;

      /**
       * The timestamp at which this account link will expire.
       */
      expires_at: number;

      /**
       * The URL for the account link.
       */
      url: string;
    }
  }
}
