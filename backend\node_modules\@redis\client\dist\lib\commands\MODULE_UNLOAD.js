"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Constructs the MODULE UNLOAD command
     *
     * @param parser - The command parser
     * @param name - The name of the module to unload
     * @see https://redis.io/commands/module-unload/
     */
    parseCommand(parser, name) {
        parser.push('MODULE', 'UNLOAD', name);
    },
    transformReply: undefined
};
//# sourceMappingURL=MODULE_UNLOAD.js.map