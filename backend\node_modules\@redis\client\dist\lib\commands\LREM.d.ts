import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the LREM command
     *
     * @param parser - The command parser
     * @param key - The key of the list
     * @param count - The count of elements to remove (negative: from tail to head, 0: all occurrences, positive: from head to tail)
     * @param element - The element to remove
     * @see https://redis.io/commands/lrem/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, count: number, element: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=LREM.d.ts.map