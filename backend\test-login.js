const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('./models/User');

const testLogin = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Check if users exist
    const users = await User.find({});
    console.log(`📊 Found ${users.length} users in database:`);
    
    users.forEach(user => {
      console.log(`- ${user.email} (${user.role})`);
    });

    // Test password verification for admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (adminUser) {
      console.log('\n🔐 Testing <NAME_EMAIL>...');
      const isMatch = await bcrypt.compare('123456', adminUser.password);
      console.log(`Password match: ${isMatch}`);
      
      if (!isMatch) {
        console.log('❌ Password does not match! Let me check the stored hash...');
        console.log('Stored hash:', adminUser.password);
        
        // Try to create a new hash and compare
        const newHash = await bcrypt.hash('123456', 12);
        console.log('New hash for "123456":', newHash);
        const testMatch = await bcrypt.compare('123456', newHash);
        console.log('Test hash match:', testMatch);
      }
    } else {
      console.log('❌ Admin user not found!');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
};

testLogin();
