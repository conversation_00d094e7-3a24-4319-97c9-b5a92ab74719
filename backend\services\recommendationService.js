const Booking = require('../models/Booking');
const Service = require('../models/Service');
const Review = require('../models/Review');
const User = require('../models/User');

class RecommendationService {
  // Get personalized service recommendations for a user
  async getPersonalizedRecommendations(userId, limit = 5) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get user's booking history
      const userBookings = await Booking.find({ customer: userId })
        .populate('service', 'category name')
        .sort({ createdAt: -1 });

      // Get user's preferred categories based on booking history
      const categoryPreferences = this.calculateCategoryPreferences(userBookings);

      // Get highly rated services
      const highRatedServices = await this.getHighRatedServices();

      // Get trending services
      const trendingServices = await this.getTrendingServices();

      // Get seasonal recommendations
      const seasonalServices = await this.getSeasonalRecommendations();

      // Combine and score recommendations
      const recommendations = await this.combineAndScoreRecommendations({
        categoryPreferences,
        highRatedServices,
        trendingServices,
        seasonalServices,
        userBookings
      });

      // Filter out services user has recently booked
      const recentServiceIds = userBookings
        .filter(booking => {
          const daysSince = (Date.now() - booking.createdAt) / (1000 * 60 * 60 * 24);
          return daysSince < 90; // Within last 90 days
        })
        .map(booking => booking.service._id.toString());

      const filteredRecommendations = recommendations
        .filter(rec => !recentServiceIds.includes(rec.service._id.toString()))
        .slice(0, limit);

      return filteredRecommendations;
    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      throw error;
    }
  }

  // Calculate user's category preferences based on booking history
  calculateCategoryPreferences(userBookings) {
    const categoryCount = {};
    const totalBookings = userBookings.length;

    userBookings.forEach(booking => {
      const category = booking.service.category;
      categoryCount[category] = (categoryCount[category] || 0) + 1;
    });

    // Convert to preferences (0-1 scale)
    const preferences = {};
    Object.keys(categoryCount).forEach(category => {
      preferences[category] = categoryCount[category] / totalBookings;
    });

    return preferences;
  }

  // Get services with high ratings
  async getHighRatedServices() {
    try {
      const highRatedServices = await Review.aggregate([
        {
          $group: {
            _id: '$service',
            avgRating: { $avg: '$rating' },
            reviewCount: { $sum: 1 }
          }
        },
        {
          $match: {
            avgRating: { $gte: 4.0 },
            reviewCount: { $gte: 3 }
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: '_id',
            foreignField: '_id',
            as: 'serviceInfo'
          }
        },
        {
          $unwind: '$serviceInfo'
        },
        {
          $match: {
            'serviceInfo.isActive': true
          }
        },
        {
          $sort: { avgRating: -1, reviewCount: -1 }
        },
        {
          $limit: 10
        }
      ]);

      return highRatedServices.map(item => ({
        service: item.serviceInfo,
        score: item.avgRating / 5, // Normalize to 0-1
        reason: `Highly rated (${item.avgRating.toFixed(1)}/5 stars)`
      }));
    } catch (error) {
      console.error('Error getting high rated services:', error);
      return [];
    }
  }

  // Get trending services based on recent booking frequency
  async getTrendingServices() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const trendingServices = await Booking.aggregate([
        {
          $match: {
            createdAt: { $gte: thirtyDaysAgo }
          }
        },
        {
          $group: {
            _id: '$service',
            bookingCount: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: '_id',
            foreignField: '_id',
            as: 'serviceInfo'
          }
        },
        {
          $unwind: '$serviceInfo'
        },
        {
          $match: {
            'serviceInfo.isActive': true
          }
        },
        {
          $sort: { bookingCount: -1 }
        },
        {
          $limit: 10
        }
      ]);

      const maxBookings = trendingServices[0]?.bookingCount || 1;

      return trendingServices.map(item => ({
        service: item.serviceInfo,
        score: item.bookingCount / maxBookings,
        reason: `Trending (${item.bookingCount} recent bookings)`
      }));
    } catch (error) {
      console.error('Error getting trending services:', error);
      return [];
    }
  }

  // Get seasonal service recommendations
  async getSeasonalRecommendations() {
    try {
      const currentMonth = new Date().getMonth() + 1; // 1-12
      const seasonalServices = [];

      // Define seasonal service mappings
      const seasonalMappings = {
        // Winter (Dec, Jan, Feb)
        12: ['hvac', 'heating'],
        1: ['hvac', 'heating'],
        2: ['hvac', 'heating'],
        // Spring (Mar, Apr, May)
        3: ['plumbing', 'general'],
        4: ['electrical', 'general'],
        5: ['hvac', 'general'],
        // Summer (Jun, Jul, Aug)
        6: ['hvac', 'cooling'],
        7: ['hvac', 'cooling'],
        8: ['hvac', 'cooling'],
        // Fall (Sep, Oct, Nov)
        9: ['hvac', 'general'],
        10: ['heating', 'general'],
        11: ['hvac', 'heating']
      };

      const seasonalCategories = seasonalMappings[currentMonth] || ['general'];

      const services = await Service.find({
        category: { $in: seasonalCategories },
        isActive: true
      }).limit(5);

      return services.map(service => ({
        service,
        score: 0.7, // Moderate seasonal boost
        reason: 'Seasonal recommendation'
      }));
    } catch (error) {
      console.error('Error getting seasonal recommendations:', error);
      return [];
    }
  }

  // Combine and score all recommendations
  async combineAndScoreRecommendations({
    categoryPreferences,
    highRatedServices,
    trendingServices,
    seasonalServices,
    userBookings
  }) {
    const serviceScores = new Map();

    // Weight factors
    const weights = {
      categoryPreference: 0.4,
      rating: 0.3,
      trending: 0.2,
      seasonal: 0.1
    };

    // Process high-rated services
    highRatedServices.forEach(item => {
      const serviceId = item.service._id.toString();
      const categoryPref = categoryPreferences[item.service.category] || 0;
      
      const score = (
        weights.categoryPreference * categoryPref +
        weights.rating * item.score
      );

      serviceScores.set(serviceId, {
        service: item.service,
        score,
        reasons: [item.reason]
      });
    });

    // Process trending services
    trendingServices.forEach(item => {
      const serviceId = item.service._id.toString();
      const categoryPref = categoryPreferences[item.service.category] || 0;
      
      const baseScore = (
        weights.categoryPreference * categoryPref +
        weights.trending * item.score
      );

      if (serviceScores.has(serviceId)) {
        const existing = serviceScores.get(serviceId);
        existing.score = Math.max(existing.score, baseScore);
        existing.reasons.push(item.reason);
      } else {
        serviceScores.set(serviceId, {
          service: item.service,
          score: baseScore,
          reasons: [item.reason]
        });
      }
    });

    // Process seasonal services
    seasonalServices.forEach(item => {
      const serviceId = item.service._id.toString();
      const categoryPref = categoryPreferences[item.service.category] || 0;
      
      const baseScore = (
        weights.categoryPreference * categoryPref +
        weights.seasonal * item.score
      );

      if (serviceScores.has(serviceId)) {
        const existing = serviceScores.get(serviceId);
        existing.score += weights.seasonal * item.score; // Add seasonal boost
        existing.reasons.push(item.reason);
      } else {
        serviceScores.set(serviceId, {
          service: item.service,
          score: baseScore,
          reasons: [item.reason]
        });
      }
    });

    // Convert to array and sort by score
    return Array.from(serviceScores.values())
      .sort((a, b) => b.score - a.score);
  }

  // Get similar users based on booking patterns
  async getSimilarUsers(userId, limit = 10) {
    try {
      const userBookings = await Booking.find({ customer: userId })
        .populate('service', 'category');

      const userCategories = userBookings.map(b => b.service.category);
      const uniqueUserCategories = [...new Set(userCategories)];

      // Find users with similar category preferences
      const similarUsers = await Booking.aggregate([
        {
          $match: {
            customer: { $ne: userId }
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: 'service',
            foreignField: '_id',
            as: 'serviceInfo'
          }
        },
        {
          $unwind: '$serviceInfo'
        },
        {
          $match: {
            'serviceInfo.category': { $in: uniqueUserCategories }
          }
        },
        {
          $group: {
            _id: '$customer',
            commonCategories: { $addToSet: '$serviceInfo.category' },
            bookingCount: { $sum: 1 }
          }
        },
        {
          $addFields: {
            similarity: {
              $size: {
                $setIntersection: ['$commonCategories', uniqueUserCategories]
              }
            }
          }
        },
        {
          $match: {
            similarity: { $gte: 1 }
          }
        },
        {
          $sort: { similarity: -1, bookingCount: -1 }
        },
        {
          $limit: limit
        }
      ]);

      return similarUsers;
    } catch (error) {
      console.error('Error finding similar users:', error);
      return [];
    }
  }

  // Get recommendations based on similar users
  async getCollaborativeRecommendations(userId, limit = 5) {
    try {
      const similarUsers = await this.getSimilarUsers(userId);
      
      if (similarUsers.length === 0) {
        return [];
      }

      const similarUserIds = similarUsers.map(u => u._id);

      // Get services booked by similar users
      const recommendations = await Booking.aggregate([
        {
          $match: {
            customer: { $in: similarUserIds }
          }
        },
        {
          $group: {
            _id: '$service',
            bookingCount: { $sum: 1 },
            users: { $addToSet: '$customer' }
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: '_id',
            foreignField: '_id',
            as: 'serviceInfo'
          }
        },
        {
          $unwind: '$serviceInfo'
        },
        {
          $match: {
            'serviceInfo.isActive': true
          }
        },
        {
          $sort: { bookingCount: -1 }
        },
        {
          $limit: limit
        }
      ]);

      return recommendations.map(item => ({
        service: item.serviceInfo,
        score: item.bookingCount / similarUsers.length,
        reason: `Popular among similar users (${item.bookingCount} bookings)`
      }));
    } catch (error) {
      console.error('Error getting collaborative recommendations:', error);
      return [];
    }
  }
}

module.exports = new RecommendationService();
