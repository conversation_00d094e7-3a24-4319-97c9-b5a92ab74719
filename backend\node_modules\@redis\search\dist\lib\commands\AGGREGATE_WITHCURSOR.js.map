{"version": 3, "file": "AGGREGATE_WITHCURSOR.js", "sourceRoot": "", "sources": ["../../../lib/commands/AGGREGATE_WITHCURSOR.ts"], "names": [], "mappings": ";;;;;AAEA,4DAA+F;AAiB/F,kBAAe;IACb,YAAY,EAAE,mBAAS,CAAC,YAAY;IACpC;;;;;;;;;OASG;IACH,YAAY,CAAC,MAAqB,EAAE,KAAoB,EAAE,KAAoB,EAAE,OAAsC;QACpH,mBAAS,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1B,IAAI,OAAO,EAAE,KAAK,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,IAAG,OAAO,EAAE,OAAO,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IACD,cAAc,EAAE;QACd,CAAC,EAAE,CAAC,KAAkC,EAA4B,EAAE;YAClE,OAAO;gBACL,GAAG,mBAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;aACjB,CAAC;QACJ,CAAC;QACD,CAAC,EAAE,SAAwC;KAC5C;IACD,aAAa,EAAE,IAAI;CACO,CAAC"}