{"name": "repair-maintenance-backend", "version": "1.0.0", "description": "Backend API for repair and maintenance services app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seed.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "socket.io": "^4.8.1", "stripe": "^18.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["repair", "maintenance", "api", "nodejs", "express", "mongodb"], "author": "Your Name", "license": "MIT"}