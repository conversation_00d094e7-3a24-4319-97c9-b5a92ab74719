import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Returns a serialized version of the value stored at the key
     * @param parser - The Redis command parser
     * @param key - Key to dump
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply;
};
export default _default;
//# sourceMappingURL=DUMP.d.ts.map