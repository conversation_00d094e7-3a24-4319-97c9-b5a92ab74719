const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// In-memory users for testing
const users = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // "password"
    phone: '1234567890',
    role: 'customer',
    isActive: true
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // "password"
    phone: '1234567891',
    role: 'technician',
    isActive: true
  },
  {
    id: '3',
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // "password"
    phone: '1234567892',
    role: 'admin',
    isActive: true
  },
  {
    id: '4',
    firstName: 'Test',
    lastName: 'Customer',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // "password"
    phone: '**********',
    role: 'customer',
    isActive: true
  }
];

// Generate JWT token
const generateToken = (userId) => {
  return 'fake-jwt-token-' + userId;
};

// Routes
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Repair & Maintenance Services API - Minimal Version',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Backend server is running (minimal version)',
    timestamp: new Date().toISOString(),
    dbConnected: false,
    status: 'healthy'
  });
});

// Auth routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('Login attempt:', { email, password });
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // Find user
    const user = users.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (!user) {
      console.log('User not found:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    
    if (!isMatch) {
      console.log('Password mismatch for:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Generate token
    const token = generateToken(user.id);

    console.log('Login successful for:', email);

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        role: user.role,
        isActive: user.isActive
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login'
    });
  }
});

app.post('/api/auth/register', async (req, res) => {
  try {
    const { firstName, lastName, email, password, phone, role = 'customer' } = req.body;
    
    console.log('Registration attempt:', { firstName, lastName, email, role });
    
    // Check if user exists
    const existingUser = users.find(u => u.email.toLowerCase() === email.toLowerCase());
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create new user
    const newUser = {
      id: (users.length + 1).toString(),
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      role,
      isActive: true
    };

    users.push(newUser);

    // Generate token
    const token = generateToken(newUser.id);

    console.log('Registration successful for:', email);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: newUser.id,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email,
        phone: newUser.phone,
        role: newUser.role,
        isActive: newUser.isActive
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
});

// Mock services endpoint
app.get('/api/services', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        _id: '1',
        name: 'Pipe Leak Repair',
        description: 'Professional pipe leak detection and repair',
        category: 'plumbing',
        basePrice: 120,
        estimatedDuration: 2,
        isActive: true
      },
      {
        _id: '2',
        name: 'Electrical Outlet Installation',
        description: 'Safe electrical outlet installation',
        category: 'electrical',
        basePrice: 150,
        estimatedDuration: 1.5,
        isActive: true
      }
    ]
  });
});

// Catch all
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`🚀 Minimal server running on port ${PORT}`);
  console.log(`📍 API available at: http://localhost:${PORT}/api`);
  console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
  console.log('');
  console.log('🔑 Test Login Credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password');
  console.log('');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password');
});

module.exports = app;
