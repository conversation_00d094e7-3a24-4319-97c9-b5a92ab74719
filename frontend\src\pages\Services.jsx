import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import { serviceService } from '../services/serviceService';
import { MagnifyingGlassIcon, FunnelIcon } from '../components/Icons';
import LoadingSpinner, { InlineLoader } from '../components/LoadingSpinner';
import { formatFCFA } from '../utils/currency';

const Services = () => {
  const [services, setServices] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    minPrice: '',
    maxPrice: '',
    difficulty: ''
  });

  useEffect(() => {
    fetchServices();
    fetchCategories();
  }, [filters]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await serviceService.getServices(filters);
      setServices(response.data || []);
    } catch (error) {
      console.error('Failed to fetch services:', error);
      setError(error.userMessage || 'Failed to load services. Please try again.');

      // For demo purposes, set some mock data if API fails
      setServices([
        {
          _id: '1',
          name: 'Pipe Leak Repair',
          description: 'Professional pipe leak detection and repair service. We fix all types of pipe leaks including kitchen, bathroom, and basement pipes.',
          category: 'plumbing',
          basePrice: 120,
          estimatedDuration: 2,
          difficulty: 'medium'
        },
        {
          _id: '2',
          name: 'Electrical Outlet Installation',
          description: 'Safe and professional electrical outlet installation. Perfect for adding new outlets or replacing old ones.',
          category: 'electrical',
          basePrice: 150,
          estimatedDuration: 1.5,
          difficulty: 'medium'
        },
        {
          _id: '3',
          name: 'AC Unit Maintenance',
          description: 'Complete air conditioning unit maintenance including cleaning, filter replacement, and performance check.',
          category: 'hvac',
          basePrice: 200,
          estimatedDuration: 3,
          difficulty: 'medium'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await serviceService.getCategories();
      setCategories(response.data || []);
    } catch (error) {
      console.error('Failed to fetch categories');
      // Set mock categories if API fails
      setCategories([
        { value: 'plumbing', label: 'Plumbing', icon: '🔧' },
        { value: 'electrical', label: 'Electrical', icon: '⚡' },
        { value: 'hvac', label: 'HVAC', icon: '❄️' },
        { value: 'appliance', label: 'Appliance Repair', icon: '🔌' },
        { value: 'carpentry', label: 'Carpentry', icon: '🔨' },
        { value: 'painting', label: 'Painting', icon: '🎨' },
        { value: 'roofing', label: 'Roofing', icon: '🏠' },
        { value: 'cleaning', label: 'Cleaning', icon: '🧽' },
        { value: 'other', label: 'Other', icon: '🛠️' }
      ]);
    }
  };

  const handleFilterChange = (e) => {
    setFilters({
      ...filters,
      [e.target.name]: e.target.value
    });
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      minPrice: '',
      maxPrice: '',
      difficulty: ''
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
            <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <p className="text-gray-600 font-medium">Loading services...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container">
        {/* Modern Header */}
        <div className="text-center mb-16 animate-fadeInUp">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Services</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Professional repair and maintenance services for your home and office needs. Expert technicians, modern solutions, guaranteed results.
          </p>
        </div>

        {/* Modern Filters */}
        <div className="card card-elevated mb-12 animate-fadeInUp">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <FunnelIcon />
              </div>
              Filters
            </h2>
            <button
              onClick={clearFilters}
              className="btn btn-ghost text-primary-600 hover:text-primary-700"
            >
              Clear all
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            {/* Search */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="text-gray-400" />
              </div>
              <input
                type="text"
                name="search"
                placeholder="Search services..."
                className="form-input pl-10"
                value={filters.search}
                onChange={handleFilterChange}
              />
            </div>

            {/* Category */}
            <select
              name="category"
              className="form-input form-select"
              value={filters.category}
              onChange={handleFilterChange}
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.icon} {category.label}
                </option>
              ))}
          </select>

          {/* Price Range */}
          <input
            type="number"
            name="minPrice"
            placeholder="Min Price"
            className="form-input"
            value={filters.minPrice}
            onChange={handleFilterChange}
          />

          <input
            type="number"
            name="maxPrice"
            placeholder="Max Price"
            className="form-input"
            value={filters.maxPrice}
            onChange={handleFilterChange}
          />

          {/* Difficulty */}
          <select
            name="difficulty"
            className="form-input"
            value={filters.difficulty}
            onChange={handleFilterChange}
          >
            <option value="">All Difficulties</option>
            <option value="easy">Easy</option>
            <option value="medium">Medium</option>
            <option value="hard">Hard</option>
          </select>
        </div>
      </div>

      {/* Services Grid */}
      {loading ? (
        <div className="text-center py-8">
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto'
          }}></div>
          <p className="text-gray-600 mt-4">Loading services...</p>
        </div>
      ) : services.length === 0 ? (
        <div className="text-center py-12">
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🔧</div>
          <p className="text-gray-500 text-lg">No services found matching your criteria.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3" style={{ gap: '1.5rem' }}>
          {services.map((service) => (
            <div key={service._id} className="card" style={{
              overflow: 'hidden',
              transition: 'box-shadow 0.2s'
            }}>
              <div style={{
                height: '12rem',
                backgroundColor: '#f3f4f6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '1.5rem'
              }}>
                <span style={{ fontSize: '3rem' }}>
                  {categories.find(cat => cat.value === service.category)?.icon || '🛠️'}
                </span>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
                  <span style={{
                    padding: '0.25rem 0.5rem',
                    borderRadius: '9999px',
                    fontSize: '0.75rem',
                    backgroundColor:
                      service.difficulty === 'easy' ? '#dcfce7' :
                      service.difficulty === 'medium' ? '#fef3c7' : '#fee2e2',
                    color:
                      service.difficulty === 'easy' ? '#166534' :
                      service.difficulty === 'medium' ? '#92400e' : '#991b1b'
                  }}>
                    {service.difficulty}
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}>
                  {service.description}
                </p>

                <div className="flex items-center justify-between mb-4">
                  <span className="text-2xl font-bold" style={{ color: '#2563eb' }}>
                    {formatFCFA(service.basePrice)}
                  </span>
                  <span className="text-sm text-gray-500">
                    ~{service.estimatedDuration}h
                  </span>
                </div>

                <div className="flex" style={{ gap: '0.5rem' }}>
                  <Link
                    to={`/services/${service._id}`}
                    className="btn btn-secondary"
                    style={{
                      flex: 1,
                      textAlign: 'center',
                      textDecoration: 'none'
                    }}
                  >
                    View Details
                  </Link>
                  <Link
                    to={`/book/${service._id}`}
                    className="btn btn-primary"
                    style={{
                      flex: 1,
                      textAlign: 'center',
                      textDecoration: 'none'
                    }}
                  >
                    Book Now
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      </div>
    </div>
  );
};

export default Services;
