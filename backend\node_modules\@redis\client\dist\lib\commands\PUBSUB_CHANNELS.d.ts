import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the PUBSUB CHANNELS command
     *
     * @param parser - The command parser
     * @param pattern - Optional pattern to filter channels
     * @see https://redis.io/commands/pubsub-channels/
     */
    readonly parseCommand: (this: void, parser: CommandParser, pattern?: RedisArgument) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=PUBSUB_CHANNELS.d.ts.map