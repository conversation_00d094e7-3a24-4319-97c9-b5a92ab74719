import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the RENAME command
     *
     * @param parser - The command parser
     * @param key - The key to rename
     * @param newKey - The new key name
     * @see https://redis.io/commands/rename/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, newKey: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply;
};
export default _default;
//# sourceMappingURL=RENAME.d.ts.map