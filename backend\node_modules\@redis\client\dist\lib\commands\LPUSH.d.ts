import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    /**
     * Constructs the LPUSH command
     *
     * @param parser - The command parser
     * @param key - The key of the list
     * @param elements - One or more elements to push to the list
     * @see https://redis.io/commands/lpush/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, elements: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=LPUSH.d.ts.map