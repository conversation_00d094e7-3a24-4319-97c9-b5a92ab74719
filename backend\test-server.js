const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Test login route
app.post('/api/auth/login', (req, res) => {
  console.log('Login request received:', req.body);
  
  const { email, password } = req.body;
  
  // Simple test credentials
  if (email === '<EMAIL>' && password === '123456') {
    res.json({
      success: true,
      message: 'Login successful',
      token: 'test-token-123',
      user: {
        id: '1',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'customer'
      }
    });
  } else {
    console.log('Invalid credentials:', { email, password });
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ success: true, message: 'Server is running' });
});

// Start server
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`🚀 Test server running on port ${PORT}`);
  console.log('🔑 Test credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: 123456');
});
