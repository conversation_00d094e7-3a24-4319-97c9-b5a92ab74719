"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const INCRBY_1 = __importDefault(require("./INCRBY"));
const INFO_1 = __importDefault(require("./INFO"));
const INITBYDIM_1 = __importDefault(require("./INITBYDIM"));
const INITBYPROB_1 = __importDefault(require("./INITBYPROB"));
const MERGE_1 = __importDefault(require("./MERGE"));
const QUERY_1 = __importDefault(require("./QUERY"));
exports.default = {
    INCRBY: INCRBY_1.default,
    incrBy: INCRBY_1.default,
    INFO: INFO_1.default,
    info: INFO_1.default,
    INITBYDIM: INITBYDIM_1.default,
    initByDim: INITBYDIM_1.default,
    INITBYPROB: INITBYPROB_1.default,
    initByProb: INITBYPROB_1.default,
    MERGE: MERGE_1.default,
    merge: MERGE_1.default,
    QUERY: QUERY_1.default,
    query: QUERY_1.default
};
//# sourceMappingURL=index.js.map