import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, SimpleStringReply } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Adds an alias to a RediSearch index.
     * @param parser - The command parser
     * @param alias - The alias to add
     * @param index - The index name to alias
     */
    readonly parseCommand: (this: void, parser: CommandParser, alias: RedisArgument, index: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=ALIASADD.d.ts.map