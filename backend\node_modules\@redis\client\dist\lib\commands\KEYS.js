"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Constructs the KEYS command
     *
     * @param parser - The command parser
     * @param pattern - The pattern to match keys against
     * @see https://redis.io/commands/keys/
     */
    parseCommand(parser, pattern) {
        parser.push('KEYS', pattern);
    },
    transformReply: undefined
};
//# sourceMappingURL=KEYS.js.map