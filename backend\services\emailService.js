const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  initializeTransporter() {
    if (!process.env.EMAIL_HOST || !process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.warn('Email configuration not found. Email notifications will be disabled.');
      return;
    }

    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Verify connection
    this.transporter.verify((error, success) => {
      if (error) {
        console.error('Email service connection failed:', error);
      } else {
        console.log('✅ Email service ready');
      }
    });
  }

  async sendEmail(to, subject, html, text = null) {
    if (!this.transporter) {
      console.log('Email service not configured. Skipping email:', subject);
      return { success: false, message: 'Email service not configured' };
    }

    try {
      const mailOptions = {
        from: `"Repair & Maintenance Services" <${process.env.EMAIL_USER}>`,
        to,
        subject,
        html,
        text: text || this.stripHtml(html)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Email sending failed:', error);
      return { success: false, error: error.message };
    }
  }

  // Booking confirmation email
  async sendBookingConfirmation(booking, customer) {
    const subject = `Booking Confirmation - ${booking.service.name}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Booking Confirmed!</h2>
        
        <p>Dear ${customer.firstName} ${customer.lastName},</p>
        
        <p>Your booking has been confirmed. Here are the details:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Booking Details</h3>
          <p><strong>Service:</strong> ${booking.service.name}</p>
          <p><strong>Category:</strong> ${booking.service.category}</p>
          <p><strong>Date:</strong> ${new Date(booking.scheduledDate).toLocaleDateString()}</p>
          <p><strong>Time:</strong> ${booking.scheduledTime?.start || 'TBD'}</p>
          <p><strong>Total Amount:</strong> $${booking.pricing?.totalAmount || booking.totalAmount}</p>
          <p><strong>Status:</strong> ${booking.status}</p>
        </div>
        
        <p>We will contact you soon to confirm the exact time and assign a technician.</p>
        
        <p>If you have any questions, please don't hesitate to contact us.</p>
        
        <p>Best regards,<br>Repair & Maintenance Services Team</p>
      </div>
    `;

    return await this.sendEmail(customer.email, subject, html);
  }

  // Booking status update email
  async sendBookingStatusUpdate(booking, customer, oldStatus) {
    const subject = `Booking Status Updated - ${booking.service.name}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Booking Status Updated</h2>
        
        <p>Dear ${customer.firstName} ${customer.lastName},</p>
        
        <p>Your booking status has been updated:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Service:</strong> ${booking.service.name}</p>
          <p><strong>Previous Status:</strong> <span style="color: #6b7280;">${oldStatus}</span></p>
          <p><strong>Current Status:</strong> <span style="color: #059669;">${booking.status}</span></p>
          ${booking.technician ? `<p><strong>Technician:</strong> ${booking.technician.firstName} ${booking.technician.lastName}</p>` : ''}
        </div>
        
        ${this.getStatusMessage(booking.status)}
        
        <p>Best regards,<br>Repair & Maintenance Services Team</p>
      </div>
    `;

    return await this.sendEmail(customer.email, subject, html);
  }

  // Payment confirmation email
  async sendPaymentConfirmation(booking, customer, paymentAmount) {
    const subject = `Payment Confirmed - ${booking.service.name}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #059669;">Payment Confirmed!</h2>
        
        <p>Dear ${customer.firstName} ${customer.lastName},</p>
        
        <p>We have successfully received your payment.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Payment Details</h3>
          <p><strong>Service:</strong> ${booking.service.name}</p>
          <p><strong>Amount Paid:</strong> $${paymentAmount}</p>
          <p><strong>Payment Date:</strong> ${new Date().toLocaleDateString()}</p>
          <p><strong>Booking ID:</strong> ${booking._id}</p>
        </div>
        
        <p>Your service is now confirmed and we will proceed with the scheduled appointment.</p>
        
        <p>Best regards,<br>Repair & Maintenance Services Team</p>
      </div>
    `;

    return await this.sendEmail(customer.email, subject, html);
  }

  // Technician assignment email
  async sendTechnicianAssignment(booking, technician) {
    const subject = `New Service Assignment - ${booking.service.name}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">New Service Assignment</h2>
        
        <p>Dear ${technician.firstName} ${technician.lastName},</p>
        
        <p>You have been assigned a new service booking:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Booking Details</h3>
          <p><strong>Service:</strong> ${booking.service.name}</p>
          <p><strong>Customer:</strong> ${booking.customer.firstName} ${booking.customer.lastName}</p>
          <p><strong>Date:</strong> ${new Date(booking.scheduledDate).toLocaleDateString()}</p>
          <p><strong>Time:</strong> ${booking.scheduledTime?.start || 'TBD'}</p>
          <p><strong>Address:</strong> ${this.formatAddress(booking.serviceAddress)}</p>
          <p><strong>Problem Description:</strong> ${booking.problemDescription}</p>
        </div>
        
        <p>Please log in to your dashboard to view more details and update the booking status.</p>
        
        <p>Best regards,<br>Repair & Maintenance Services Team</p>
      </div>
    `;

    return await this.sendEmail(technician.email, subject, html);
  }

  // Welcome email for new users
  async sendWelcomeEmail(user) {
    const subject = 'Welcome to Repair & Maintenance Services!';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to Repair & Maintenance Services!</h2>
        
        <p>Dear ${user.firstName} ${user.lastName},</p>
        
        <p>Thank you for joining our platform! We're excited to help you with all your repair and maintenance needs.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">What you can do:</h3>
          <ul>
            <li>Browse our wide range of repair and maintenance services</li>
            <li>Book services with qualified technicians</li>
            <li>Track your booking status in real-time</li>
            <li>Rate and review completed services</li>
            <li>Manage your profile and preferences</li>
          </ul>
        </div>
        
        <p>Get started by browsing our services and booking your first appointment!</p>
        
        <p>If you have any questions, our support team is here to help.</p>
        
        <p>Best regards,<br>Repair & Maintenance Services Team</p>
      </div>
    `;

    return await this.sendEmail(user.email, subject, html);
  }

  // Helper methods
  getStatusMessage(status) {
    const messages = {
      'confirmed': '<p style="color: #059669;">Your booking has been confirmed and we will contact you soon with technician details.</p>',
      'assigned': '<p style="color: #059669;">A technician has been assigned to your booking and will contact you shortly.</p>',
      'in-progress': '<p style="color: #2563eb;">Your service is currently in progress. The technician is working on your request.</p>',
      'completed': '<p style="color: #059669;">Your service has been completed! Please rate your experience in your dashboard.</p>',
      'cancelled': '<p style="color: #dc2626;">Your booking has been cancelled. If you have any questions, please contact us.</p>'
    };
    return messages[status] || '';
  }

  formatAddress(address) {
    if (!address) return 'Address not provided';
    return `${address.street || ''}, ${address.city || ''}, ${address.state || ''} ${address.zipCode || ''}`.replace(/^,\s*|,\s*$/g, '');
  }

  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }
}

module.exports = new EmailService();
