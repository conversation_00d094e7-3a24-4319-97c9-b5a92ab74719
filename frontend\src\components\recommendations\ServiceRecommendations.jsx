import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import api from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { formatFCFA } from '../../utils/currency';

const ServiceRecommendations = ({ type = 'personalized', limit = 5, title }) => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    fetchRecommendations();
  }, [type, limit, user]);

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      
      let endpoint = '/recommendations/trending'; // Default for non-authenticated users
      
      if (user && type === 'personalized') {
        endpoint = '/recommendations/personalized';
      } else if (user && type === 'collaborative') {
        endpoint = '/recommendations/collaborative';
      } else if (type === 'seasonal') {
        endpoint = '/recommendations/seasonal';
      }
      
      const response = await api.get(`${endpoint}?limit=${limit}`);
      setRecommendations(response.data);
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      if (error.response?.status !== 401) {
        toast.error('Failed to load recommendations');
      }
    } finally {
      setLoading(false);
    }
  };

  const getRecommendationIcon = (reasons) => {
    if (!reasons || reasons.length === 0) return '🔧';
    
    const reason = reasons[0].toLowerCase();
    if (reason.includes('highly rated')) return '⭐';
    if (reason.includes('trending')) return '🔥';
    if (reason.includes('seasonal')) return '🌟';
    if (reason.includes('similar users')) return '👥';
    return '🔧';
  };

  const getDefaultTitle = () => {
    switch (type) {
      case 'personalized':
        return 'Recommended for You';
      case 'collaborative':
        return 'Others Also Booked';
      case 'seasonal':
        return 'Seasonal Services';
      case 'trending':
        return 'Trending Services';
      default:
        return 'Recommended Services';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex space-x-4">
                <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (recommendations.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {title || getDefaultTitle()}
        </h3>
        {type === 'personalized' && user && (
          <span className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
            AI Powered
          </span>
        )}
      </div>

      <div className="space-y-4">
        {recommendations.map((recommendation, index) => (
          <div
            key={recommendation.service._id}
            className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all"
          >
            {/* Service Icon/Image */}
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-xl">
                  {getRecommendationIcon(recommendation.reasons)}
                </span>
              </div>
            </div>

            {/* Service Info */}
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {recommendation.service.name}
              </h4>
              <p className="text-sm text-gray-600 truncate">
                {recommendation.service.description}
              </p>
              
              {/* Recommendation Reasons */}
              {recommendation.reasons && recommendation.reasons.length > 0 && (
                <div className="mt-1 flex flex-wrap gap-1">
                  {recommendation.reasons.slice(0, 2).map((reason, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {reason}
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Price and Action */}
            <div className="flex-shrink-0 text-right">
              <div className="text-sm font-medium text-gray-900">
                {formatFCFA(recommendation.service.price)}
              </div>
              <Link
                to={`/services/${recommendation.service._id}`}
                className="inline-flex items-center mt-1 text-xs text-blue-600 hover:text-blue-800"
              >
                View Details
                <svg className="ml-1 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* View All Link */}
      <div className="mt-4 text-center">
        <Link
          to="/services"
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          View All Services →
        </Link>
      </div>
    </div>
  );
};

export default ServiceRecommendations;
