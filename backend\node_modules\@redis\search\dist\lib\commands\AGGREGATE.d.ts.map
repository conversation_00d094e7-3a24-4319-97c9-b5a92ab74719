{"version": 3, "file": "AGGREGATE.d.ts", "sourceRoot": "", "sources": ["../../../lib/commands/AGGREGATE.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,sCAAsC,CAAC;AACrE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAW,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,mCAAmC,CAAC;AACrK,OAAO,EAAE,kBAAkB,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,EAAE,cAAc,EAAuB,MAAM,UAAU,CAAC;AAI/D,KAAK,SAAS,GAAG,kBAAkB,GAAG;IACpC,UAAU,EAAE,kBAAkB,CAAC;IAC/B,EAAE,CAAC,EAAE,aAAa,CAAC;CACpB,CAAA;AAED,eAAO,MAAM,kBAAkB;;;;;;CAMrB,CAAC;AAEX,KAAK,kBAAkB,GAAG,OAAO,kBAAkB,CAAC;AAEpD,MAAM,MAAM,eAAe,GAAG,kBAAkB,CAAC,MAAM,kBAAkB,CAAC,CAAC;AAE3E,UAAU,aAAa,CAAC,CAAC,SAAS,eAAe;IAC/C,IAAI,EAAE,CAAC,CAAC;CACT;AAED,eAAO,MAAM,8BAA8B;;;;;;;;;;;;;CAajC,CAAC;AAEX,KAAK,8BAA8B,GAAG,OAAO,8BAA8B,CAAC;AAE5E,MAAM,MAAM,yBAAyB,GAAG,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,CAAC;AAE7G,UAAU,cAAc,CAAC,CAAC,SAAS,yBAAyB;IAC1D,IAAI,EAAE,CAAC,CAAC;IACR,EAAE,CAAC,EAAE,aAAa,CAAC;CACpB;AAED,UAAU,0BAA0B,CAAC,CAAC,SAAS,yBAAyB,CAAE,SAAQ,cAAc,CAAC,CAAC,CAAC;IACjG,QAAQ,EAAE,kBAAkB,CAAC;CAC9B;AAED,KAAK,YAAY,GAAG,cAAc,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC,CAAC;AAE5E,KAAK,oBAAoB,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAEzG,KAAK,uBAAuB,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAE/G,KAAK,UAAU,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;AAEpF,KAAK,UAAU,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;AAEpF,KAAK,UAAU,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;AAEpF,KAAK,UAAU,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;AAEpF,KAAK,aAAa,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE1F,UAAU,eAAgB,SAAQ,0BAA0B,CAAC,8BAA8B,CAAC,UAAU,CAAC,CAAC;IACtG,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,KAAK,aAAa,GAAG,0BAA0B,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE1F,UAAU,iBAAkB,SAAQ,0BAA0B,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC;IAC3G,EAAE,CAAC,EAAE,kBAAkB,GAAG;QACxB,QAAQ,EAAE,kBAAkB,CAAC;QAC7B,SAAS,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;KAC5B,CAAC;CACH;AAED,UAAU,mBAAoB,SAAQ,0BAA0B,CAAC,8BAA8B,CAAC,eAAe,CAAC,CAAC;IAC/G,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,KAAK,eAAe,GAAG,YAAY,GAAG,oBAAoB,GAAG,uBAAuB,GAAG,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,aAAa,GAAG,eAAe,GAAG,aAAa,GAAG,iBAAiB,GAAG,mBAAmB,CAAC;AAErO,UAAU,WAAY,SAAQ,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACxE,UAAU,CAAC,EAAE,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAC5D,MAAM,EAAE,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;CAClD;AAED,KAAK,cAAc,GAAG,aAAa,GAAG;IACpC,EAAE,EAAE,kBAAkB,CAAC;IACvB,SAAS,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;CAC5B,CAAC;AAEF,UAAU,QAAS,SAAQ,aAAa,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IACpE,EAAE,EAAE,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;IAC3C,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAED,UAAU,SAAU,SAAQ,aAAa,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACpE,UAAU,EAAE,aAAa,CAAC;IAC1B,EAAE,EAAE,aAAa,CAAC;CACnB;AAED,UAAU,SAAU,SAAQ,aAAa,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACpE,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,UAAU,UAAW,SAAQ,aAAa,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IACtE,UAAU,EAAE,aAAa,CAAC;CAC3B;AAED,MAAM,WAAW,kBAAkB;IACjC,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,IAAI,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;IACpC,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,KAAK,CAAC,WAAW,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC;IAC3E,MAAM,CAAC,EAAE,cAAc,CAAC;IACxB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,MAAM,iBAAiB,GAAG;IAC9B,KAAK,EAAE,WAAW,CAAC,WAAW,CAAC;IAC/B,GAAG,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;CACjE,CAAC;AAEF,MAAM,WAAW,cAAc;IAC7B,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC;CAC5D;;;;IAKC;;;;;;;;;;;OAWG;gDACkB,aAAa,SAAS,aAAa,SAAS,aAAa,YAAY,kBAAkB;;wIAM9D,GAAG,gBAAgB,WAAW,KAAG,cAAc;0BAgB1D,UAAU;;;;AArC/C,wBAwC6B;AAE7B,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,aAAa,EAAG,OAAO,CAAC,EAAE,kBAAkB,QA0FzF"}