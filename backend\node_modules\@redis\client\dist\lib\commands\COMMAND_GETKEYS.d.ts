import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Extracts the key names from a Redis command
     * @param parser - The Redis command parser
     * @param args - Command arguments to analyze
     */
    readonly parseCommand: (this: void, parser: CommandParser, args: Array<RedisArgument>) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=COMMAND_GETKEYS.d.ts.map