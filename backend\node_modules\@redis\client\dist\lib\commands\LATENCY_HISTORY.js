"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Constructs the LATENCY HISTORY command
     *
     * @param parser - The command parser
     * @param event - The latency event to get the history for
     * @see https://redis.io/commands/latency-history/
     */
    parseCommand(parser, event) {
        parser.push('LATENCY', 'HISTORY', event);
    },
    transformReply: undefined
};
//# sourceMappingURL=LATENCY_HISTORY.js.map