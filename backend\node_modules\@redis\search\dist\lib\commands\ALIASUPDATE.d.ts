import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { SimpleStringReply, RedisArgument } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Updates the index pointed to by an existing alias.
     * @param parser - The command parser
     * @param alias - The existing alias to update
     * @param index - The new index name that the alias should point to
     */
    readonly parseCommand: (this: void, parser: CommandParser, alias: RedisArgument, index: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=ALIASUPDATE.d.ts.map