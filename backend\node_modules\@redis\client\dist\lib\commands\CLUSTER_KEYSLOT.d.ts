import { CommandParser } from '../client/parser';
import { NumberReply, RedisArgument } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns the hash slot number for a given key
     * @param parser - The Redis command parser
     * @param key - The key to get the hash slot for
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=CLUSTER_KEYSLOT.d.ts.map