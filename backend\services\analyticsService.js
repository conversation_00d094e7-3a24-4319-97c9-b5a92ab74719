const User = require('../models/User');
const Booking = require('../models/Booking');
const Service = require('../models/Service');
const Review = require('../models/Review');

class AnalyticsService {
  // Get comprehensive business analytics
  async getBusinessAnalytics(period = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const analytics = await Promise.all([
        this.getUserAnalytics(startDate),
        this.getBookingAnalytics(startDate),
        this.getRevenueAnalytics(startDate),
        this.getServiceAnalytics(startDate),
        this.getCustomerSatisfactionAnalytics(startDate),
        this.getOperationalAnalytics(startDate),
        this.getGrowthAnalytics(startDate)
      ]);

      return {
        users: analytics[0],
        bookings: analytics[1],
        revenue: analytics[2],
        services: analytics[3],
        satisfaction: analytics[4],
        operations: analytics[5],
        growth: analytics[6],
        period
      };
    } catch (error) {
      console.error('Error getting business analytics:', error);
      throw error;
    }
  }

  // User analytics
  async getUserAnalytics(startDate) {
    const [totalUsers, newUsers, activeUsers, usersByRole, userRetention] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ createdAt: { $gte: startDate } }),
      this.getActiveUsers(startDate),
      this.getUsersByRole(),
      this.getUserRetentionRate(startDate)
    ]);

    return {
      total: totalUsers,
      new: newUsers,
      active: activeUsers,
      byRole: usersByRole,
      retention: userRetention
    };
  }

  // Booking analytics
  async getBookingAnalytics(startDate) {
    const [totalBookings, newBookings, bookingsByStatus, bookingTrends, avgBookingValue] = await Promise.all([
      Booking.countDocuments(),
      Booking.countDocuments({ createdAt: { $gte: startDate } }),
      this.getBookingsByStatus(),
      this.getBookingTrends(startDate),
      this.getAverageBookingValue(startDate)
    ]);

    return {
      total: totalBookings,
      new: newBookings,
      byStatus: bookingsByStatus,
      trends: bookingTrends,
      averageValue: avgBookingValue
    };
  }

  // Revenue analytics
  async getRevenueAnalytics(startDate) {
    const [totalRevenue, periodRevenue, revenueByCategory, revenueGrowth, projectedRevenue] = await Promise.all([
      this.getTotalRevenue(),
      this.getPeriodRevenue(startDate),
      this.getRevenueByCategory(startDate),
      this.getRevenueGrowth(startDate),
      this.getProjectedRevenue()
    ]);

    return {
      total: totalRevenue,
      period: periodRevenue,
      byCategory: revenueByCategory,
      growth: revenueGrowth,
      projected: projectedRevenue
    };
  }

  // Service analytics
  async getServiceAnalytics(startDate) {
    const [popularServices, servicePerformance, categoryDistribution, serviceRatings] = await Promise.all([
      this.getPopularServices(startDate),
      this.getServicePerformance(startDate),
      this.getCategoryDistribution(),
      this.getServiceRatings()
    ]);

    return {
      popular: popularServices,
      performance: servicePerformance,
      categoryDistribution,
      ratings: serviceRatings
    };
  }

  // Customer satisfaction analytics
  async getCustomerSatisfactionAnalytics(startDate) {
    const [overallRating, ratingDistribution, satisfactionTrends, npsScore] = await Promise.all([
      this.getOverallRating(),
      this.getRatingDistribution(startDate),
      this.getSatisfactionTrends(startDate),
      this.getNPSScore(startDate)
    ]);

    return {
      overall: overallRating,
      distribution: ratingDistribution,
      trends: satisfactionTrends,
      nps: npsScore
    };
  }

  // Operational analytics
  async getOperationalAnalytics(startDate) {
    const [completionRate, avgCompletionTime, technicianUtilization, peakHours] = await Promise.all([
      this.getCompletionRate(startDate),
      this.getAverageCompletionTime(startDate),
      this.getTechnicianUtilization(startDate),
      this.getPeakBookingHours(startDate)
    ]);

    return {
      completionRate,
      avgCompletionTime,
      technicianUtilization,
      peakHours
    };
  }

  // Growth analytics
  async getGrowthAnalytics(startDate) {
    const [userGrowth, revenueGrowth, bookingGrowth, marketShare] = await Promise.all([
      this.getUserGrowthRate(startDate),
      this.getRevenueGrowthRate(startDate),
      this.getBookingGrowthRate(startDate),
      this.getMarketShareAnalysis()
    ]);

    return {
      users: userGrowth,
      revenue: revenueGrowth,
      bookings: bookingGrowth,
      marketShare
    };
  }

  // Helper methods
  async getActiveUsers(startDate) {
    return await User.countDocuments({
      $or: [
        { lastLoginAt: { $gte: startDate } },
        { createdAt: { $gte: startDate } }
      ]
    });
  }

  async getUsersByRole() {
    return await User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);
  }

  async getUserRetentionRate(startDate) {
    const thirtyDaysAgo = new Date(startDate);
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [newUsers, returningUsers] = await Promise.all([
      User.countDocuments({ createdAt: { $gte: thirtyDaysAgo, $lt: startDate } }),
      User.countDocuments({
        createdAt: { $gte: thirtyDaysAgo, $lt: startDate },
        lastLoginAt: { $gte: startDate }
      })
    ]);

    return newUsers > 0 ? (returningUsers / newUsers) * 100 : 0;
  }

  async getBookingsByStatus() {
    return await Booking.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
  }

  async getBookingTrends(startDate) {
    return await Booking.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);
  }

  async getAverageBookingValue(startDate) {
    const result = await Booking.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      { $group: { _id: null, avg: { $avg: '$pricing.totalAmount' } } }
    ]);
    return result[0]?.avg || 0;
  }

  async getTotalRevenue() {
    const result = await Booking.aggregate([
      { $match: { status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$pricing.totalAmount' } } }
    ]);
    return result[0]?.total || 0;
  }

  async getPeriodRevenue(startDate) {
    const result = await Booking.aggregate([
      {
        $match: {
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      { $group: { _id: null, total: { $sum: '$pricing.totalAmount' } } }
    ]);
    return result[0]?.total || 0;
  }

  async getRevenueByCategory(startDate) {
    return await Booking.aggregate([
      {
        $match: {
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $lookup: {
          from: 'services',
          localField: 'service',
          foreignField: '_id',
          as: 'serviceInfo'
        }
      },
      { $unwind: '$serviceInfo' },
      {
        $group: {
          _id: '$serviceInfo.category',
          revenue: { $sum: '$pricing.totalAmount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { revenue: -1 } }
    ]);
  }

  async getPopularServices(startDate) {
    return await Booking.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      { $group: { _id: '$service', count: { $sum: 1 } } },
      {
        $lookup: {
          from: 'services',
          localField: '_id',
          foreignField: '_id',
          as: 'serviceInfo'
        }
      },
      { $unwind: '$serviceInfo' },
      {
        $project: {
          name: '$serviceInfo.name',
          category: '$serviceInfo.category',
          count: 1
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
  }

  async getOverallRating() {
    const result = await Review.aggregate([
      { $group: { _id: null, avg: { $avg: '$rating' } } }
    ]);
    return result[0]?.avg || 0;
  }

  async getCompletionRate(startDate) {
    const [total, completed] = await Promise.all([
      Booking.countDocuments({ createdAt: { $gte: startDate } }),
      Booking.countDocuments({ 
        createdAt: { $gte: startDate },
        status: 'completed'
      })
    ]);
    return total > 0 ? (completed / total) * 100 : 0;
  }

  // Additional helper methods would be implemented here...
  async getRevenueGrowth(startDate) {
    // Implementation for revenue growth calculation
    return { rate: 15.5, trend: 'up' }; // Placeholder
  }

  async getProjectedRevenue() {
    // Implementation for revenue projection
    return 125000; // Placeholder
  }

  async getServicePerformance(startDate) {
    // Implementation for service performance metrics
    return []; // Placeholder
  }

  async getCategoryDistribution() {
    // Implementation for category distribution
    return []; // Placeholder
  }

  async getServiceRatings() {
    // Implementation for service ratings
    return []; // Placeholder
  }

  async getRatingDistribution(startDate) {
    // Implementation for rating distribution
    return []; // Placeholder
  }

  async getSatisfactionTrends(startDate) {
    // Implementation for satisfaction trends
    return []; // Placeholder
  }

  async getNPSScore(startDate) {
    // Implementation for NPS score calculation
    return 75; // Placeholder
  }

  async getAverageCompletionTime(startDate) {
    // Implementation for average completion time
    return 2.5; // Placeholder (hours)
  }

  async getTechnicianUtilization(startDate) {
    // Implementation for technician utilization
    return 85; // Placeholder (percentage)
  }

  async getPeakBookingHours(startDate) {
    // Implementation for peak booking hours
    return []; // Placeholder
  }

  async getUserGrowthRate(startDate) {
    // Implementation for user growth rate
    return 12.3; // Placeholder
  }

  async getRevenueGrowthRate(startDate) {
    // Implementation for revenue growth rate
    return 18.7; // Placeholder
  }

  async getBookingGrowthRate(startDate) {
    // Implementation for booking growth rate
    return 22.1; // Placeholder
  }

  async getMarketShareAnalysis() {
    // Implementation for market share analysis
    return { share: 15.2, trend: 'growing' }; // Placeholder
  }
}

module.exports = new AnalyticsService();
