declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Invokes a read-only Redis function
     * @param parser - The Redis command parser
     * @param functionName - Name of the function to call
     * @param options - Function execution options including keys and arguments
     */
    readonly parseCommand: (this: void, parser: import("../..").CommandParser, script: import("../RESP/types").RedisArgument, options?: import("./EVAL").EvalOptions | undefined) => void;
    readonly transformReply: () => import("../RESP/types").ReplyUnion;
};
export default _default;
//# sourceMappingURL=FCALL_RO.d.ts.map