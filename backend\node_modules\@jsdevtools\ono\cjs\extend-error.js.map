{"version": 3, "file": "extend-error.js", "sourceRoot": "", "sources": ["../src/extend-error.ts"], "names": [], "mappings": ";;;AAAA,uDAAqD;AACrD,mCAAmF;AACnF,uCAAgD;AAGhD,MAAM,cAAc,GAA2B,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AAE5E;;;;;;GAMG;AACH,SAAgB,WAAW,CAA6D,KAAQ,EAAE,aAAiB,EAAE,KAAS;IAC5H,IAAI,QAAQ,GAAG,KAAmD,CAAC;IAEnE,WAAW,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IAErC,0CAA0C;IAC1C,IAAI,aAAa,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACtD,WAAW,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;KACtC;IAED,yFAAyF;IACzF,mEAAmE;IACnE,QAAQ,CAAC,MAAM,GAAG,gBAAM,CAAC;IAEzB,0DAA0D;IAC1D,uEAAuE;IACvE,IAAI,kCAAgB,EAAE;QACpB,kCAAgB,CAAC,QAAQ,CAAC,CAAC;KAC5B;IAED,mEAAmE;IACnE,2CAA2C;IAC3C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KAChC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AA3BD,kCA2BC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,QAAmB,EAAE,aAAyB;IACjE,IAAI,SAAS,GAAG,MAAM,CAAC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAEnE,IAAI,mBAAW,CAAC,SAAS,CAAC,EAAE;QAC1B,sBAAc,CAAC,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;KACpD;SACI,IAAI,uBAAe,CAAC,SAAS,CAAC,EAAE;QACnC,QAAQ,CAAC,KAAK,GAAG,kBAAU,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;KACtD;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,WAAW,CAAC,QAAmB,EAAE,aAAwB;IAChE,gCAAgC;IAChC,sFAAsF;IACtF,qFAAqF;IACrF,qCAAqC;IACrC,IAAI,IAAI,GAAG,qBAAW,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IAEtD,2EAA2E;IAC3E,0DAA0D;IAC1D,IAAI,SAAS,GAAG,QAAe,CAAC;IAChC,IAAI,cAAc,GAAG,aAAoB,CAAC;IAE1C,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;QACpB,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAChC,IAAI;gBACF,SAAS,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;aACtC;YACD,OAAO,CAAC,EAAE;gBACR,oDAAoD;aACrD;SACF;KACF;AACH,CAAC"}