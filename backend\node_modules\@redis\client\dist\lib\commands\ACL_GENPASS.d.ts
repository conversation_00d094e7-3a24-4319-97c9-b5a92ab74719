import { CommandParser } from '../client/parser';
import { BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Generates a secure password for ACL users
     * @param parser - The Redis command parser
     * @param bits - Optional number of bits for password entropy
     */
    readonly parseCommand: (this: void, parser: CommandParser, bits?: number) => void;
    readonly transformReply: () => BlobStringReply;
};
export default _default;
//# sourceMappingURL=ACL_GENPASS.d.ts.map