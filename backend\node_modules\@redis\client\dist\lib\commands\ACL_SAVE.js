"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Saves the current ACL configuration to the ACL file
     * @param parser - The Redis command parser
     */
    parseCommand(parser) {
        parser.push('ACL', 'SAVE');
    },
    transformReply: undefined
};
//# sourceMappingURL=ACL_SAVE.js.map