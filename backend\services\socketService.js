const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
    this.userSockets = new Map(); // socketId -> userId
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production'
          ? ['https://yourdomain.com']
          : ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],
        credentials: true
      }
    });

    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('Authentication error'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user._id.toString();
        socket.user = user;
        next();
      } catch (error) {
        next(new Error('Authentication error'));
      }
    });

    this.io.on('connection', (socket) => {
      console.log(`User ${socket.user.firstName} ${socket.user.lastName} connected`);
      
      // Store user connection
      this.connectedUsers.set(socket.userId, socket.id);
      this.userSockets.set(socket.id, socket.userId);

      // Join user to their personal room
      socket.join(`user_${socket.userId}`);

      // Join technicians to technician room
      if (socket.user.role === 'technician') {
        socket.join('technicians');
      }

      // Join admins to admin room
      if (socket.user.role === 'admin') {
        socket.join('admins');
      }

      // Handle booking status updates
      socket.on('booking_status_update', (data) => {
        this.handleBookingStatusUpdate(socket, data);
      });

      // Handle chat messages
      socket.on('send_message', (data) => {
        this.handleChatMessage(socket, data);
      });

      // Handle technician location updates
      socket.on('location_update', (data) => {
        this.handleLocationUpdate(socket, data);
      });

      // Handle typing indicators
      socket.on('typing_start', (data) => {
        this.handleTypingStart(socket, data);
      });

      socket.on('typing_stop', (data) => {
        this.handleTypingStop(socket, data);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        console.log(`User ${socket.user.firstName} ${socket.user.lastName} disconnected`);
        this.connectedUsers.delete(socket.userId);
        this.userSockets.delete(socket.id);
      });
    });

    console.log('✅ Socket.IO server initialized');
  }

  // Emit booking status update to relevant users
  emitBookingStatusUpdate(bookingId, status, customerId, technicianId) {
    const updateData = {
      bookingId,
      status,
      timestamp: new Date()
    };

    // Notify customer
    if (customerId) {
      this.io.to(`user_${customerId}`).emit('booking_status_updated', updateData);
    }

    // Notify technician
    if (technicianId) {
      this.io.to(`user_${technicianId}`).emit('booking_status_updated', updateData);
    }

    // Notify admins
    this.io.to('admins').emit('booking_status_updated', updateData);
  }

  // Emit new booking notification to technicians
  emitNewBookingNotification(booking) {
    const notificationData = {
      bookingId: booking._id,
      service: booking.service.name,
      customer: `${booking.customer.firstName} ${booking.customer.lastName}`,
      scheduledDate: booking.scheduledDate,
      priority: booking.priority,
      timestamp: new Date()
    };

    this.io.to('technicians').emit('new_booking', notificationData);
    this.io.to('admins').emit('new_booking', notificationData);
  }

  // Emit payment confirmation
  emitPaymentConfirmation(customerId, bookingId, amount) {
    const paymentData = {
      bookingId,
      amount,
      timestamp: new Date()
    };

    this.io.to(`user_${customerId}`).emit('payment_confirmed', paymentData);
    this.io.to('admins').emit('payment_confirmed', { ...paymentData, customerId });
  }

  // Handle booking status update from socket
  handleBookingStatusUpdate(socket, data) {
    // Only technicians and admins can update booking status
    if (socket.user.role !== 'technician' && socket.user.role !== 'admin') {
      return;
    }

    // Emit to relevant users
    this.emitBookingStatusUpdate(
      data.bookingId,
      data.status,
      data.customerId,
      data.technicianId
    );
  }

  // Handle chat messages
  handleChatMessage(socket, data) {
    const messageData = {
      id: Date.now(),
      bookingId: data.bookingId,
      senderId: socket.userId,
      senderName: `${socket.user.firstName} ${socket.user.lastName}`,
      senderRole: socket.user.role,
      message: data.message,
      timestamp: new Date()
    };

    // Send to specific booking participants
    if (data.recipientId) {
      this.io.to(`user_${data.recipientId}`).emit('new_message', messageData);
    }

    // Send back to sender for confirmation
    socket.emit('message_sent', messageData);

    // Notify admins
    this.io.to('admins').emit('new_message', messageData);
  }

  // Handle technician location updates
  handleLocationUpdate(socket, data) {
    if (socket.user.role !== 'technician') {
      return;
    }

    const locationData = {
      technicianId: socket.userId,
      bookingId: data.bookingId,
      latitude: data.latitude,
      longitude: data.longitude,
      timestamp: new Date()
    };

    // Notify customer if booking is specified
    if (data.customerId) {
      this.io.to(`user_${data.customerId}`).emit('technician_location_update', locationData);
    }

    // Notify admins
    this.io.to('admins').emit('technician_location_update', locationData);
  }

  // Handle typing indicators
  handleTypingStart(socket, data) {
    const typingData = {
      userId: socket.userId,
      userName: `${socket.user.firstName} ${socket.user.lastName}`,
      bookingId: data.bookingId
    };

    if (data.recipientId) {
      this.io.to(`user_${data.recipientId}`).emit('user_typing_start', typingData);
    }
  }

  handleTypingStop(socket, data) {
    const typingData = {
      userId: socket.userId,
      bookingId: data.bookingId
    };

    if (data.recipientId) {
      this.io.to(`user_${data.recipientId}`).emit('user_typing_stop', typingData);
    }
  }

  // Send notification to specific user
  sendNotificationToUser(userId, type, data) {
    this.io.to(`user_${userId}`).emit('notification', {
      type,
      data,
      timestamp: new Date()
    });
  }

  // Send notification to all users of a specific role
  sendNotificationToRole(role, type, data) {
    const room = role === 'technician' ? 'technicians' : 'admins';
    this.io.to(room).emit('notification', {
      type,
      data,
      timestamp: new Date()
    });
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  // Check if user is online
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }

  // Get all connected users
  getConnectedUsers() {
    return Array.from(this.connectedUsers.keys());
  }
}

module.exports = new SocketService();
