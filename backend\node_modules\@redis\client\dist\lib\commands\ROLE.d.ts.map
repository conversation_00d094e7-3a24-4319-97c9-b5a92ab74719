{"version": 3, "file": "ROLE.d.ts", "sourceRoot": "", "sources": ["../../../lib/commands/ROLE.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAW,MAAM,eAAe,CAAC;AAE5G;;GAEG;AACH,KAAK,UAAU,GAAG;IAChB,IAAI,EAAE,eAAe,CAAC,QAAQ,CAAC;IAC/B,iBAAiB,EAAE,WAAW;IAC9B,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,eAAe,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC,CAAC;CACtH,CAAC;AAEF;;GAEG;AACH,KAAK,SAAS,GAAG;IACf,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC;IAC9B,UAAU,EAAE,eAAe;IAC3B,UAAU,EAAE,WAAW;IACvB,KAAK,EAAE,eAAe,CAAC,SAAS,GAAG,YAAY,GAAG,MAAM,GAAG,WAAW,CAAC;IACvE,YAAY,EAAE,WAAW;CAC1B,CAAC;AAEF;;GAEG;AACH,KAAK,YAAY,GAAG;IAClB,IAAI,EAAE,eAAe,CAAC,UAAU,CAAC;IACjC,WAAW,EAAE,UAAU,CAAC,eAAe,CAAC;CACzC,CAAC;AAEF;;GAEG;AACH,KAAK,IAAI,GAAG,WAAW,CAAC,UAAU,GAAG,SAAS,GAAG,YAAY,CAAC,CAAC;;;;IAK7D;;;;;OAKG;gDACkB,aAAa;IAGlC;;;;;OAKG;iDACmB,YAAY,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlBzC,wBA0D6B"}