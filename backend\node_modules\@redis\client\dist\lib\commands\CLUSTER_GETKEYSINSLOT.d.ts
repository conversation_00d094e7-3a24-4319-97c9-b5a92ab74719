import { CommandParser } from '../client/parser';
import { ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns a number of keys from the specified hash slot
     * @param parser - The Redis command parser
     * @param slot - The hash slot to get keys from
     * @param count - Maximum number of keys to return
     */
    readonly parseCommand: (this: void, parser: CommandParser, slot: number, count: number) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=CLUSTER_GETKEYSINSLOT.d.ts.map