import { CommandParser } from '../../client/parser';
import { RedisArgument, SimpleStringReply } from '../../RESP/types';
declare const _default: {
    /**
     * Instructs a Sentinel to monitor a new master with the specified parameters.
     * @param parser - The Redis command parser.
     * @param dbname - Name that identifies the master.
     * @param host - Host of the master.
     * @param port - Port of the master.
     * @param quorum - Number of Sentinels that need to agree to trigger a failover.
     */
    readonly parseCommand: (this: void, parser: CommandParser, dbname: RedisArgument, host: RedisArgument, port: RedisArgument, quorum: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=SENTINEL_MONITOR.d.ts.map