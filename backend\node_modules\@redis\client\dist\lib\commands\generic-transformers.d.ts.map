{"version": 3, "file": "generic-transformers.d.ts", "sourceRoot": "", "sources": ["../../../lib/commands/generic-transformers.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAErE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAE1M,wBAAgB,WAAW,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,SAAS,CAE9D;AAED,wBAAgB,YAAY,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,UAAU,CAAC,OAAO,CAAC,CAEzE;AAED,eAAO,MAAM,qBAAqB;eACrB,YAAY,CAAC,GAAG,CAAC,CAAC;aACI,YAAY;CAC9C,CAAC;AAEF,eAAO,MAAM,0BAA0B;eAC1B,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;aAGR,WAAW,YAAY,CAAC;CAC1D,CAAC;AAEF,MAAM,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;AAE7B,wBAAgB,uBAAuB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAW3D;AAED,wBAAgB,6BAA6B,CAAC,GAAG,EAAE,aAAa,GAAG,MAAM,GAAG,aAAa,CAIxF;AAED,eAAO,MAAM,oBAAoB;eACpB,eAAe,aAAa,GAAG,gBAAgB,WAAW,KAAG,WAAW;aA6BlD,WAAW;CAC7C,CAAC;AAEF,wBAAgB,mCAAmC,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,EAAE,WAAW,WAC5E,eAAe,yBAG/B;AAED,eAAO,MAAM,yBAAyB;eACzB,MAAM,eAAe,CAAC,aAAa,GAAG,gBAAgB,WAAW;aAG3C,WAAW,WAAW,CAAC;CACzD,CAAA;AAED,wBAAgB,2CAA2C,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,EAAE,WAAW,WACpF,eAAe,GAAG,SAAS,gCAG3C;AAED,eAAO,MAAM,4BAA4B;eAC5B,eAAe,GAAG,SAAS,aAAa,GAAG,gBAAgB,WAAW;aAKhD,WAAW,GAAG,SAAS;CACzD,CAAC;AAEF,MAAM,WAAW,UAAU;IACzB,QAAQ,IAAI,MAAM,CAAC;CACpB;AAED,wBAAgB,oBAAoB,CAAC,CAAC,EACpC,KAAK,EAAE,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EACnC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,OASvB;AAED,wBAAgB,8BAA8B,CAAC,CAAC,SAAS,UAAU,EAAE,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,EAAE,WAAW,WAC7F,WAAW,CAAC,CAAC,oBAG7B;AAED,wBAAgB,oBAAoB,CAAC,CAAC,SAAS,UAAU,EACvD,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,EACpB,QAAQ,CAAC,EAAE,GAAG,EACd,WAAW,CAAC,EAAE,WAAW,GACxB,QAAQ,CAAC,CAAC,EAAG,CAAC,CAAC,CA4BjB;AAED,MAAM,WAAW,eAAe;IAC9B,KAAK,EAAE,aAAa,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;CACf;AAED,MAAM,MAAM,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC;AAE1C,eAAO,MAAM,uBAAuB;eACvB,WAAW,eAAe,CAAC,aAAa,GAAG,gBAAgB,WAAW;;;;eAYtE,WAAW,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC;;;;CASnE,CAAA;AAED,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;AAExC,wBAAgB,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,MAAM,CAEzD;AAED,wBAAgB,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,GAAG,MAAM,CAEzD;AAED,MAAM,WAAW,WAAW;IAC1B,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACrB,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CAC3B;AAED,wBAAgB,iBAAiB,CAAC,OAAO,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,SAAS,CAE3E;AAED,wBAAgB,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAe3F;AAED,wBAAgB,qBAAqB,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,qBAAqB,GAAG,gBAAgB,CAS5G;AAED,wBAAgB,2BAA2B,CACzC,IAAI,EAAE,gBAAgB,EACtB,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAC5B,gBAAgB,CAUlB;AAED,MAAM,MAAM,qBAAqB,GAAG,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;AAEzE,wBAAgB,oBAAoB,CAClC,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,EAC1B,KAAK,EAAE,qBAAqB,GAC3B,gBAAgB,CAQlB;AAED,wBAAgB,6BAA6B,CAC3C,MAAM,EAAE,aAAa,EACrB,IAAI,EAAE,aAAa,EACnB,KAAK,CAAC,EAAE,qBAAqB,QAO9B;AAED,oBAAY,YAAY;IACtB,KAAK,UAAU,CAAE,sCAAsC;IACvD,QAAQ,aAAa,CAAE,iCAAiC;IACxD,OAAO,YAAY,CAAE,4CAA4C;IACjE,KAAK,UAAU,CAAE,uBAAuB;IACxC,MAAM,WAAW,CAAE,yBAAyB;IAC5C,QAAQ,aAAa,CAAE,iCAAiC;IACxD,MAAM,WAAW,CAAE,oDAAoD;IACvE,eAAe,oBAAoB,CAAE,qCAAqC;IAC1E,OAAO,YAAY,CAAE,0CAA0C;IAC/D,KAAK,UAAU,CAAE,6CAA6C;IAC9D,YAAY,iBAAiB,CAAE,sCAAsC;IACrE,MAAM,WAAW,CAAE,6CAA6C;IAChE,IAAI,SAAS,CAAE,4EAA4E;IAC3F,WAAW,gBAAgB;CAC5B;AAED,oBAAY,iBAAiB;IAC3B,QAAQ,cAAc;IACtB,IAAI,UAAU;IACd,KAAK,WAAW;IAChB,GAAG,SAAS;IACZ,SAAS,eAAe;IACxB,IAAI,UAAU;IACd,IAAI,UAAU;IACd,MAAM,YAAY;IAClB,MAAM,YAAY;IAClB,WAAW,iBAAiB;IAC5B,GAAG,SAAS;IACZ,MAAM,YAAY;IAClB,MAAM,YAAY;IAClB,KAAK,WAAW;IAChB,IAAI,UAAU;IACd,IAAI,UAAU;IACd,QAAQ,cAAc;IACtB,SAAS,eAAe;IACxB,UAAU,gBAAgB;IAC1B,WAAW,iBAAiB;IAC5B,SAAS,eAAe;CACzB;AAED,MAAM,MAAM,eAAe,GAAG;IAC5B,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC;IAC1B,aAAa,EAAE,MAAM;IACrB,YAAY,EAAE,MAAM;IACpB,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,KAAK,CAAC,iBAAiB,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;IACzB,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,MAAM,CAAC;IACrB,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,GAAG,CAAC,iBAAiB,CAAC,CAAA;CACnC,CAAC;AAEF,wBAAgB,qBAAqB,CACnC,IAAI,EAAE,IAAI,EACV,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,eAAe,GACnF,YAAY,CAUd;AAED,oBAAY,kBAAkB;IAC5B,SAAS,cAAc;IACvB,SAAS,cAAc;IACvB,WAAW,gBAAgB;IAC3B,UAAU,eAAe;CAC1B;AAED,MAAM,MAAM,wBAAwB,GAAG;IACrC,cAAc;IACd,MAAM;IACN,QAAQ;IACR,MAAM;IACN,WAAW;IACX,KAAK,CAAC;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM,GAAG,IAAI;QACb,OAAO;QACP,KAAK,CAAC,kBAAkB,CAAC;KAC1B,CAAC;CACH,CAAC;AAEF,MAAM,WAAW,qBAAqB;IACpC,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,KAAK,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;QAC3B,KAAK,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC;KAClC,CAAC,CAAC;CACJ;AAED,wBAAgB,8BAA8B,CAAC,KAAK,EAAE,wBAAwB,GAAG,qBAAqB,CAUrG;AAED,MAAM,WAAW,SAAS;IACxB,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;CACb;AAYD,wBAAgB,wBAAwB,CACtC,MAAM,EAAE,aAAa,EACrB,MAAM,EAAE,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,QASrC;AAED,MAAM,MAAM,aAAa,GAAG;IAC1B,KAAK,EAAE,MAAM;IACb,GAAG,EAAE,MAAM;CACZ,CAAC;AAEF,MAAM,WAAW,UAAU;IACzB,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;CACb;AAED,wBAAgB,mBAAmB,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,aAAa,GAAG,UAAU,CAK3E;AAED,MAAM,MAAM,aAAa,GAAG;IAC1B,GAAG,EAAE,aAAa,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAEpD,MAAM,MAAM,KAAK,GAAG,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC;AAEhF,wBAAgB,mBAAmB,CACjC,MAAM,EAAE,aAAa,EACrB,IAAI,EAAE,KAAK,QA4BZ;AAUD,MAAM,MAAM,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;AAE7F;;GAEG;AACH,wBAAgB,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,gBAAgB,CASjF;AAED,MAAM,MAAM,qBAAqB,GAAG,WAAW,CAAC;IAC9C,EAAE,EAAE,eAAe;IACnB,OAAO,EAAE,UAAU,CAAC,eAAe,CAAC;CACrC,CAAC,CAAC;AAEH,MAAM,MAAM,kBAAkB,GAAG;IAC/B,EAAE,EAAE,eAAe,CAAC;IACpB,OAAO,EAAE,QAAQ,CAAC,eAAe,GAAG,MAAM,EAAE,eAAe,CAAC,CAAC;CAC9D,CAAC;AAEF,wBAAgB,2BAA2B,CAAC,WAAW,EAAE,WAAW,GAAG,SAAS,EAAE,KAAK,EAAE,qBAAqB,GAAG,kBAAkB,CAMlI;AAED,wBAAgB,+BAA+B,CAAC,WAAW,EAAE,WAAW,GAAG,SAAS,EAAE,KAAK,EAAE,qBAAqB,GAAG,SAAS,kCAE7H;AAED,MAAM,MAAM,mBAAmB,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAE5D,MAAM,MAAM,oBAAoB,GAAG,KAAK,CAAC;IACvC,IAAI,EAAE,eAAe,GAAG,MAAM,CAAC;IAC/B,QAAQ,EAAE,mBAAmB,CAAC;CAC/B,CAAC,GAAG,IAAI,CAAC;AAEV,wBAAgB,4BAA4B,CAC1C,CAAC,EAAE,UAAU,CAAC,qBAAqB,CAAC,EACpC,WAAW,CAAC,EAAE,WAAW,GACxB,mBAAmB,CAIrB;AAED,KAAK,sBAAsB,GAAG,WAAW,CAAC,CAAC,IAAI,EAAE,eAAe,EAAE,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACtG,KAAK,wBAAwB,GAAG,UAAU,CAAC,sBAAsB,CAAC,CAAC;AAEnE,wBAAgB,kCAAkC,CAChD,KAAK,EAAE,WAAW,CAAC,wBAAwB,GAAG,SAAS,CAAC,EACxD,QAAQ,CAAC,EAAE,GAAG,EACd,WAAW,CAAC,EAAE,WAAW,GACxB,oBAAoB,GAAG,SAAS,CAmElC;AAED,KAAK,wBAAwB,GAAG,QAAQ,CAAC,eAAe,EAAE,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC;AAE7F,wBAAgB,kCAAkC,CAAC,KAAK,EAAE,WAAW,CAAC,wBAAwB,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC,eAAe,EAAE,mBAAmB,CAAC,GAAG,SAAS,CAiCvK;AAED,MAAM,MAAM,SAAS,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG;IACnF,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC;IACzB,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAC;CAC1B,CAAC;AAEF,wBAAgB,0BAA0B,CAAC,IAAI,EAAE,SAAS,GAAG,MAAM,CAElE;AAED,wBAAgB,uBAAuB,CAAC,IAAI,EAAE,eAAe,GAAG,SAAS,CAGxE;AAED,wBAAgB,2BAA2B,CAAC,IAAI,EAAE,SAAS,GAAG,eAAe,GAAG,SAAS,GAAG,SAAS,CAEpG"}