import { CommandParser } from '../client/parser';
import { NumberReply, RedisArgument } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the SISMEMBER command
     *
     * @param parser - The command parser
     * @param key - The set key to check membership in
     * @param member - The member to check for existence
     * @see https://redis.io/commands/sismember/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, member: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SISMEMBER.d.ts.map